{"name": "@repo/auth", "license": "MIT", "version": "0.1.0", "private": true, "type": "module", "exports": {"./client": {"types": "./dist/src/client.d.ts", "default": "./src/client.ts"}, "./server": {"types": "./dist/src/server.d.ts", "default": "./src/server.ts"}}, "scripts": {"build": "tsdown", "dev": "tsdown", "dev:watch": "tsdown --watch", "clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@repo/prettier-config", "dependencies": {"@repo/db": "workspace:*", "better-auth": "catalog:", "valibot": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "eslint": "catalog:", "tsdown": "catalog:", "typescript": "catalog:"}}