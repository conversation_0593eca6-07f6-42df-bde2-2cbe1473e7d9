{"name": "@repo/constants", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsdown", "dev": "tsdown", "dev:watch": "tsdown --watch", "clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm env:run drizzle-kit push", "studio": "pnpm env:run drizzle-kit studio", "typecheck": "tsc --build --noEmit --emitDeclarationOnly false", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "prettier": "@repo/prettier-config", "dependencies": {"valibot": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "catalog:", "tsdown": "catalog:", "typescript": "catalog:"}}