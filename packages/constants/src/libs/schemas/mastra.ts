export type WorkflowWatchEvent = {
  type: 'watch';
  payload: {
    currentStep?: {
      id: string;
      status: 'running' | 'success' | 'failed' | 'suspended';
      output?: Record<string, any>;
      payload?: Record<string, any>;
    };
    workflowState: {
      status: 'running' | 'success' | 'failed' | 'suspended';
      steps: Record<
        string,
        {
          status: 'running' | 'success' | 'failed' | 'suspended';
          output?: Record<string, any>;
          payload?: Record<string, any>;
        }
      >;
      output?: Record<string, any>;
      payload?: Record<string, any>;
    };
  };
  eventTimestamp: Date;
};

export interface WorkflowSnapshotState {
  runId: string;
  value: Record<string, string>;
  context: { input?: Record<string, any> } & Record<
    string,
    StepResult<any, any, any, any>
  >;
  activePaths: Array<{
    status: 'running' | 'success' | 'failed' | 'suspended';
  }>;
  suspendedPaths: Record<string, number[]>;
  timestamp: number;
}

export type StepResult<P, R, S, T> =
  | StepSuccess<P, R, S, T>
  | StepFailure<P, R, S>
  | StepSuspended<P, S>
  | StepRunning<P, R, S>;

export type StepSuccess<P, R, S, T> = {
  status: 'success';
  output: T;
  payload: P;
  resumePayload?: R;
  suspendPayload?: S;
  startedAt: number;
  endedAt: number;
  suspendedAt?: number;
  resumedAt?: number;
};

export type StepFailure<P, R, S> = {
  status: 'failed';
  error: string | Error;
  payload: P;
  resumePayload?: R;
  suspendPayload?: S;
  startedAt: number;
  endedAt: number;
  suspendedAt?: number;
  resumedAt?: number;
};

export type StepSuspended<P, S> = {
  status: 'suspended';
  payload: P;
  suspendPayload?: S;
  startedAt: number;
  suspendedAt: number;
};

export type StepRunning<P, R, S> = {
  status: 'running';
  payload: P;
  resumePayload?: R;
  suspendPayload?: S;
  startedAt: number;
  suspendedAt?: number;
  resumedAt?: number;
};
