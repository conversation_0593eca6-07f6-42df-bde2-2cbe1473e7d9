import { defineConfig } from 'tsdown';

export default defineConfig({
  entry: [
    'src/index.ts',
    'src/client.ts',
    'src/schema.ts',
    'src/operations.ts',
  ],
  format: ['esm'],
  dts: true,
  // splitting: false,
  sourcemap: true,
  clean: true,
  outDir: 'dist',
  // This ensures .js extensions are added to imports
  // esbuildOptions(options) {
  //   options.format = 'esm';
  // },
});
