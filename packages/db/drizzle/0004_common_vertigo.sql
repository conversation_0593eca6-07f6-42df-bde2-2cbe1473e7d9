--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "uid" text;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "aweme_count" bigint;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "create_time" bigint;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "region" text;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "language" text;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "user_tags" text;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "youtube_channel_id" text;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "ins_id" text;--> statement-breakpoint
ALTER TABLE "portal_creators" ADD COLUMN "twitter_id" text;--> statement-breakpoint
ALTER TABLE "mastra_messages" ADD CONSTRAINT "mastra_messages_thread_id_mastra_threads_id_fk" FOREIGN KEY ("thread_id") REFERENCES "public"."mastra_threads"("id") ON DELETE cascade ON UPDATE no action;