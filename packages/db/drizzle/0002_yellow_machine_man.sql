CREATE TABLE "workflow_context" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"trace_id" text NOT NULL,
	"context_type" text NOT NULL,
	"context_data" jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE INDEX "portal_workflow_context_trace_idx" ON "workflow_context" USING btree ("trace_id");--> statement-breakpoint
CREATE INDEX "portal_workflow_context_type_idx" ON "workflow_context" USING btree ("context_type");--> statement-breakpoint
ALTER TABLE "portal_tags" DROP COLUMN "language";--> statement-breakpoint
ALTER TABLE "portal_tags" DROP COLUMN "synonym_of";