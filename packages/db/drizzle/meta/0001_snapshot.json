{"id": "61a56db6-7ce1-43d5-a76d-92355af04671", "prevId": "ea38392e-8f60-4ffc-9f7e-703e3bacde37", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_creators": {"name": "portal_creators", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "platform_id": {"name": "platform_id", "type": "smallserial", "primaryKey": false, "notNull": true}, "platform_creator_id": {"name": "platform_creator_id", "type": "text", "primaryKey": false, "notNull": true}, "handle": {"name": "handle", "type": "text", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "profile_url": {"name": "profile_url", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "follower_count": {"name": "follower_count", "type": "bigint", "primaryKey": false, "notNull": false}, "global_creator_id": {"name": "global_creator_id", "type": "bigint", "primaryKey": false, "notNull": false}}, "indexes": {"portal_creators_platform_ext_uq": {"name": "portal_creators_platform_ext_uq", "columns": [{"expression": "platform_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "platform_creator_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"portal_creators_platform_id_portal_platforms_id_fk": {"name": "portal_creators_platform_id_portal_platforms_id_fk", "tableFrom": "portal_creators", "tableTo": "portal_platforms", "columnsFrom": ["platform_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "portal_creators_global_creator_id_portal_global_creators_id_fk": {"name": "portal_creators_global_creator_id_portal_global_creators_id_fk", "tableFrom": "portal_creators", "tableTo": "portal_global_creators", "columnsFrom": ["global_creator_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_global_creators": {"name": "portal_global_creators", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_platforms": {"name": "portal_platforms", "schema": "", "columns": {"id": {"name": "id", "type": "smallserial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"portal_platforms_name_uq": {"name": "portal_platforms_name_uq", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_tags": {"name": "portal_tags", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "text", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false}, "synonym_of": {"name": "synonym_of", "type": "bigserial", "primaryKey": false, "notNull": true}}, "indexes": {"portal_tags_tag_uq": {"name": "portal_tags_tag_uq", "columns": [{"expression": "tag", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_video_stats": {"name": "portal_video_stats", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "video_id": {"name": "video_id", "type": "bigserial", "primaryKey": false, "notNull": true}, "collected_at": {"name": "collected_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "view_count": {"name": "view_count", "type": "bigint", "primaryKey": false, "notNull": false}, "like_count": {"name": "like_count", "type": "bigint", "primaryKey": false, "notNull": false}, "comment_count": {"name": "comment_count", "type": "bigint", "primaryKey": false, "notNull": false}, "share_count": {"name": "share_count", "type": "bigint", "primaryKey": false, "notNull": false}}, "indexes": {"portal_video_stats_snapshot_uq": {"name": "portal_video_stats_snapshot_uq", "columns": [{"expression": "video_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "collected_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "portal_video_stats_video_time_idx": {"name": "portal_video_stats_video_time_idx", "columns": [{"expression": "video_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "collected_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"portal_video_stats_video_id_portal_videos_id_fk": {"name": "portal_video_stats_video_id_portal_videos_id_fk", "tableFrom": "portal_video_stats", "tableTo": "portal_videos", "columnsFrom": ["video_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_video_tags": {"name": "portal_video_tags", "schema": "", "columns": {"video_id": {"name": "video_id", "type": "bigserial", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "bigserial", "primaryKey": false, "notNull": true}, "raw_token": {"name": "raw_token", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"portal_video_tags_tag_idx": {"name": "portal_video_tags_tag_idx", "columns": [{"expression": "tag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"portal_video_tags_video_id_portal_videos_id_fk": {"name": "portal_video_tags_video_id_portal_videos_id_fk", "tableFrom": "portal_video_tags", "tableTo": "portal_videos", "columnsFrom": ["video_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "portal_video_tags_tag_id_portal_tags_id_fk": {"name": "portal_video_tags_tag_id_portal_tags_id_fk", "tableFrom": "portal_video_tags", "tableTo": "portal_tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"portal_video_tags_tag_id_raw_token_pk": {"name": "portal_video_tags_tag_id_raw_token_pk", "columns": ["tag_id", "raw_token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.portal_videos": {"name": "portal_videos", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "platform_id": {"name": "platform_id", "type": "smallserial", "primaryKey": false, "notNull": true}, "platform_video_id": {"name": "platform_video_id", "type": "text", "primaryKey": false, "notNull": true}, "creator_id": {"name": "creator_id", "type": "bigserial", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "duration_seconds": {"name": "duration_seconds", "type": "integer", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "raw_tags": {"name": "raw_tags", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}}, "indexes": {"portal_videos_platform_ext_uq": {"name": "portal_videos_platform_ext_uq", "columns": [{"expression": "platform_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "platform_video_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "portal_videos_published_idx": {"name": "portal_videos_published_idx", "columns": [{"expression": "published_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"portal_videos_platform_id_portal_platforms_id_fk": {"name": "portal_videos_platform_id_portal_platforms_id_fk", "tableFrom": "portal_videos", "tableTo": "portal_platforms", "columnsFrom": ["platform_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "portal_videos_creator_id_portal_creators_id_fk": {"name": "portal_videos_creator_id_portal_creators_id_fk", "tableFrom": "portal_videos", "tableTo": "portal_creators", "columnsFrom": ["creator_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}