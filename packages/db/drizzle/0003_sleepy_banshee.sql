CREATE TABLE "user_workflows" (
	"id" bigserial PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"workflow_run_id" text NOT NULL,
	"workflow_name" text NOT NULL,
	"status" text DEFAULT 'running' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "user_workflows" ADD CONSTRAINT "user_workflows_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "user_workflows_user_id_idx" ON "user_workflows" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_workflows_run_id_idx" ON "user_workflows" USING btree ("workflow_run_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_workflows_run_id_uq" ON "user_workflows" USING btree ("workflow_run_id");