import { relations } from 'drizzle-orm';
import {
  pgTable,
  text,
  timestamp,
  jsonb,
  integer,
  bigint,
} from 'drizzle-orm/pg-core';

export const TABLE_WORKFLOW_SNAPSHOT = 'mastra_workflow_snapshot';
export const TABLE_EVALS = 'mastra_evals';
export const TABLE_MESSAGES = 'mastra_messages';
export const TABLE_THREADS = 'mastra_threads';
export const TABLE_TRACES = 'mastra_traces';

// --- Mastra Workflow Snapshot ---
export const mastraWorkflowSnapshot = pgTable(TABLE_WORKFLOW_SNAPSHOT, {
  workflow_name: text('workflow_name').notNull(),
  run_id: text('run_id').notNull(),
  snapshot: text('snapshot').notNull(),
  createdAt: timestamp('createdAt', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updatedAt', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

// --- <PERSON><PERSON> Evals ---
export const mastraEvals = pgTable(TABLE_EVALS, {
  input: text('input').notNull(),
  output: text('output').notNull(),
  result: jsonb('result').notNull(),
  agent_name: text('agent_name').notNull(),
  metric_name: text('metric_name').notNull(),
  instructions: text('instructions').notNull(),
  test_info: jsonb('test_info'),
  global_run_id: text('global_run_id').notNull(),
  run_id: text('run_id').notNull(),
  created_at: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(), // Note: original had created_at and createdAt
  createdAt: timestamp('createdAt', { withTimezone: true }), // Kept nullable as per original schema
});

// --- Mastra Threads ---
export const mastraThreads = pgTable(TABLE_THREADS, {
  id: text('id').primaryKey(),
  resourceId: text('resourceId').notNull(),
  title: text('title').notNull(),
  metadata: text('metadata'),
  createdAt: timestamp('createdAt', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updatedAt', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

export const mastraThreadsRelations = relations(mastraThreads, ({ many }) => ({
  messages: many(mastraMessages),
}));

// --- Mastra Messages ---
export const mastraMessages = pgTable(TABLE_MESSAGES, {
  id: text('id').primaryKey(),
  thread_id: text('thread_id')
    .notNull()
    .references(() => mastraThreads.id, { onDelete: 'cascade' }),
  content: text('content').notNull(),
  role: text('role').notNull(),
  type: text('type').notNull(),
  createdAt: timestamp('createdAt', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

export const mastraMessagesRelations = relations(mastraMessages, ({ one }) => ({
  thread: one(mastraThreads, {
    fields: [mastraMessages.thread_id],
    references: [mastraThreads.id],
  }),
}));

// --- Mastra Traces ---
export const mastraTraces = pgTable(TABLE_TRACES, {
  id: text('id').primaryKey(),
  parentSpanId: text('parentSpanId'),
  name: text('name').notNull(),
  traceId: text('traceId').notNull(),
  scope: text('scope').notNull(),
  kind: integer('kind').notNull(),
  attributes: jsonb('attributes'),
  status: jsonb('status'),
  events: jsonb('events'),
  links: jsonb('links'),
  other: text('other'),
  startTime: bigint('startTime', { mode: 'bigint' }).notNull(), // Use bigint for nanoseconds precision
  endTime: bigint('endTime', { mode: 'bigint' }).notNull(), // Use bigint for nanoseconds precision
  createdAt: timestamp('createdAt', { withTimezone: true })
    .notNull()
    .defaultNow(),
});
