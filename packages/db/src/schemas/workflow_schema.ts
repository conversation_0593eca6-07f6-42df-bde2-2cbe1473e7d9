import {
  bigserial,
  index,
  jsonb,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { user } from './auth_schema';

export const workflowContext = pgTable(
  'workflow_context',
  {
    id: bigserial('id', { mode: 'number' }).primaryKey(),
    traceId: text('trace_id').notNull(),
    contextType: text('context_type').notNull(),
    contextData: jsonb('context_data').notNull().$type(),
    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
  },
  (t) => [
    index('portal_workflow_context_trace_idx').on(t.traceId),
    index('portal_workflow_context_type_idx').on(t.contextType),
  ],
);

export const userWorkflows = pgTable(
  'user_workflows',
  {
    id: bigserial('id', { mode: 'number' }).primaryKey(),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
    workflowRunId: text('workflow_run_id').notNull(), // This is the trace_id/run_id from Mastra
    workflowName: text('workflow_name').notNull(),
    status: text('status').notNull().default('running'), // running, completed, failed
    createdAt: timestamp('created_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .notNull(),
  },
  (t) => [
    index('user_workflows_user_id_idx').on(t.userId),
    index('user_workflows_run_id_idx').on(t.workflowRunId),
    uniqueIndex('user_workflows_run_id_uq').on(t.workflowRunId),
  ],
);
