import { and, eq, desc } from 'drizzle-orm';
import type { DatabaseInstance } from '../client';
import { mastraWorkflowSnapshot } from '../schema';
import { userWorkflows, workflowContext } from '../schemas/workflow_schema';

/**
 * Database service for handling workflow-related operations
 */
export class WorkflowDb {
  constructor(private db: DatabaseInstance) {
    this.db = db;
  }

  /**
   * Save workflow context data
   * @param traceId The workflow trace ID
   * @param contextType The type of context
   * @param contextData The context data to save
   * @returns The ID of the created context
   */
  async saveWorkflowContext(
    traceId: string,
    contextType: string,
    contextData: any,
  ) {
    const result = await this.db
      .insert(workflowContext)
      .values({
        traceId,
        contextType,
        contextData,
      })
      .returning({ id: workflowContext.id });

    if (!result[0]) {
      throw new Error('Failed to save workflow context');
    }

    return result[0].id;
  }

  /**
   * Get workflow context data by trace ID and type
   * @param traceId The workflow trace ID
   * @param contextType The type of context to retrieve
   * @returns The context data or null if not found
   */
  async getWorkflowContext(traceId: string, contextType: string) {
    const contexts = await this.db
      .select()
      .from(workflowContext)
      .where(
        and(
          eq(workflowContext.traceId, traceId),
          eq(workflowContext.contextType, contextType),
        ),
      );

    if (!contexts || contexts.length <= 0) {
      return null;
    }

    const first = contexts[0]!;
    return {
      id: first.id,
      traceId: first.traceId,
      contextType: first.contextType,
      contextData: first.contextData as any,
      createdAt: first.createdAt,
    };
  }

  /**
   * Get all workflow context data by trace ID
   * @param traceId The workflow trace ID
   * @returns Array of context data
   */
  async getAllWorkflowContexts(traceId: string) {
    const contexts = await this.db
      .select()
      .from(workflowContext)
      .where(eq(workflowContext.traceId, traceId));

    return contexts.map((context) => ({
      id: context.id,
      traceId: context.traceId,
      contextType: context.contextType,
      contextData: context.contextData as any,
      createdAt: context.createdAt,
    }));
  }

  /**
   * Get workflow context data by ID
   * @param contextId The context ID
   * @returns The context data or null if not found
   */
  async getContextById(contextId: number) {
    const context = await this.db
      .select()
      .from(workflowContext)
      .where(eq(workflowContext.id, contextId));

    if (!context || context.length <= 0) {
      return null;
    }

    const first = context[0]!;
    return {
      id: first.id,
      traceId: first.traceId,
      contextType: first.contextType,
      contextData: first.contextData as any,
      createdAt: first.createdAt,
    };
  }

  /**
   * Link a workflow run to a user
   * @param userId The user ID
   * @param workflowRunId The workflow run ID (trace ID)
   * @param workflowName The name of the workflow
   * @returns The ID of the created record
   */
  async linkWorkflowToUser(
    userId: string,
    workflowRunId: string,
    workflowName: string,
  ) {
    const result = await this.db
      .insert(userWorkflows)
      .values({
        userId,
        workflowRunId,
        workflowName,
      })
      .returning({ id: userWorkflows.id });

    if (!result[0]) {
      throw new Error('Failed to link workflow to user');
    }

    return result[0].id;
  }

  /**
   * Get all workflows for a user
   * @param userId The user ID
   * @returns Array of user workflow records
   */
  async getUserWorkflows(userId: string) {
    return await this.db
      .select()
      .from(userWorkflows)
      .where(eq(userWorkflows.userId, userId))
      .orderBy(desc(userWorkflows.createdAt));
  }

  /**
   * Get a specific workflow by run ID
   * @param workflowRunId The workflow run ID
   * @returns The workflow record or null if not found
   */
  async getWorkflowByRunId(workflowRunId: string) {
    const workflows = await this.db
      .select()
      .from(userWorkflows)
      .where(eq(userWorkflows.workflowRunId, workflowRunId));

    return workflows.length > 0 ? workflows[0] : null;
  }

  /**
   * Get a specific workflow snapshot by run ID
   * @param workflowRunId The workflow run ID
   * @returns The workflow record or null if not found
   */
  async getWorkflowSnapshot(workflowRunId: string) {
    const workflows = await this.db
      .select()
      .from(mastraWorkflowSnapshot)
      .where(eq(mastraWorkflowSnapshot.run_id, workflowRunId));

    return workflows.length > 0 ? workflows[0] : null;
  }

  /**
   * Update the status of a workflow
   * @param workflowRunId The workflow run ID
   * @param status The new status (running, completed, failed)
   */
  async updateWorkflowStatus(workflowRunId: string, status: string) {
    await this.db
      .update(userWorkflows)
      .set({
        status,
        updatedAt: new Date(),
      })
      .where(eq(userWorkflows.workflowRunId, workflowRunId));
  }

  /**
   * Update the name of a workflow
   * @param workflowRunId The workflow run ID
   * @param workflowName The new workflow name
   */
  async updateWorkflowName(workflowRunId: string, workflowName: string) {
    await this.db
      .update(userWorkflows)
      .set({
        workflowName,
        updatedAt: new Date(),
      })
      .where(eq(userWorkflows.workflowRunId, workflowRunId));
  }
}

export const createWorkflowDbService = (db: DatabaseInstance) => {
  return new WorkflowDb(db);
};
// Export a singleton instance
// export const workflowDbService = new WorkflowDb();
