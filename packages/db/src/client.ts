import { drizzle, NodePgDatabase } from 'drizzle-orm/node-postgres';

// import env from './env';
import * as schema from './schema';

export interface DatabaseClientOptions {
  databaseUrl?: string;
  max?: number;
}

export type DatabaseInstance = NodePgDatabase<typeof schema>;

export const createDb = (opts?: DatabaseClientOptions): DatabaseInstance => {
  return drizzle({
    schema,
    casing: 'snake_case',
    connection: {
      connectionString: opts?.databaseUrl,
      max: opts?.max,
    },
  });
};

// export const db = createDb({
//   databaseUrl: env.DB_POSTGRES_URL,
//   max: env.DB_MAX_CONNECTIONS,
// });
