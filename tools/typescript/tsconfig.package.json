{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    /* Base Options: */
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "es2022",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,

    /* Strictness */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    // "noImplicitOverride": true,

    /* AND if you're building for a library in a monorepo: */
    // "composite": true,
    "declaration": true,
    "declarationMap": true,

    /* If NOT transpiling with TypeScript: */
    "module": "Preserve",
    "noEmit": true,

    /* If your code doesn't run in the DOM: */
    "lib": ["es2022"]
  }
}
