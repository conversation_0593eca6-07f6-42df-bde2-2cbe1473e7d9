{"name": "@joaimono/source", "private": true, "type": "module", "scripts": {"auth:schema:generate": "turbo run auth:schema:generate", "postauth:schema:generate": "echo NOTE: you will also need to fix styles and db:push your new schema", "build": "turbo run build", "clean": "turbo run clean", "db:push": "turbo -F @repo/db push", "db:studio": "turbo -F @repo/db studio", "dev": "turbo watch dev --continue", "env:copy-example": "turbo run env:copy-example", "env:remove": "turbo run env:remove", "format": "turbo run format --continue -- --cache --cache-location .cache/.prettiercache", "format:fix": "pnpm format --write", "lint": "turbo run lint --continue -- --cache --cache-location .cache/.eslintcache", "lint:fix": "pnpm lint --fix", "postclean": "git clean -xdf .cache .turbo node_modules", "start": "turbo run start", "typecheck": "turbo run typecheck"}, "packageManager": "pnpm@10.12.1", "prettier": "@repo/prettier-config", "devDependencies": {"@repo/prettier-config": "workspace:*", "prettier": "catalog:", "turbo": "catalog:"}, "engines": {"node": ">=22.10.0"}, "pnpm": {"overrides": {"@babel/helpers@<7.26.10": "^7.26.10", "cookie@<0.7.0": "^0.7.0", "esbuild@<0.25.0": "^0.25.0"}, "onlyBuiltDependencies": [], "ignoredBuiltDependencies": ["@swc/core", "esbuild"]}}