{"name": "@joaimono/agent", "version": "0.1.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "type": "module", "dependencies": {"@ai-sdk/google": "catalog:", "@aws-sdk/client-s3": "catalog:", "@aws-sdk/lib-storage": "catalog:", "@inquirer/prompts": "catalog:", "@mastra/core": "catalog:", "@mastra/loggers": "catalog:", "@mastra/memory": "catalog:", "@mastra/pg": "catalog:", "@neondatabase/serverless": "catalog:", "@openrouter/ai-sdk-provider": "catalog:", "@repo/constants": "workspace:*", "@repo/db": "workspace:*", "ai": "catalog:", "axios": "catalog:", "valibot": "catalog:", "xlsx": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "@types/xlsx": "catalog:", "dotenv": "catalog:", "mastra": "catalog:", "typescript": "catalog:"}}