import { Memory } from '@mastra/memory';
import { PostgresStore } from '@mastra/pg';
import { env } from './env';
import { google } from '@ai-sdk/google';

const connectionString = env.DB_POSTGRES_URL;

export const storage = new PostgresStore({
  connectionString,
});

const agentMemory = new Memory({
  options: {
    lastMessages: 50,
    threads: {
      generateTitle: true,
    },
  },
  storage: storage,
  //
  //   vector: new PgVector({ connectionString }),
  // vector: new PgVector({ connectionString }),
  embedder: google.textEmbeddingModel('gemini-embedding-exp-03-07'),
});

export default agentMemory;
