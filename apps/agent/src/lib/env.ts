import * as v from 'valibot';
import * as dotenv from 'dotenv';

dotenv.config();

export const envSchema = v.object({
  DB_POSTGRES_URL: v.string(),
  GOOGLE_GENERATIVE_AI_API_KEY: v.string(),
  TIKHUB_API_KEY: v.string(),
  RAPIDAPI_KEY: v.string(),
  OPENROUTER_API_KEY: v.string(),
  PREFERRED_TIKTOK_SERVICE: v.optional(
    v.union([v.literal('tikhub'), v.literal('tokapi')]),
  ),
  // S3 OSS Configuration
  S3_ACCESS_KEY_ID: v.string(),
  S3_SECRET_ACCESS_KEY: v.string(),
  S3_REGION: v.string(),
  S3_BUCKET_NAME: v.string(),
  S3_ENDPOINT: v.optional(v.string()),

  // Upload Configuration
  MAX_UPLOAD_CONCURRENT_JOBS: v.optional(v.string()),
});

export type Env = v.InferInput<typeof envSchema>;

let env: Env;
try {
  const parsedEnv = v.parse(envSchema, process.env);
  env = parsedEnv;
} catch (error) {
  throw new Error(`Invalid environment variables: ${error}`);
}

export { env };
