import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { videoScouter } from "@/services/scouting/videoScouter";

/**
 * Tool for searching TikTok challenges/hashtags
 */
export const searchTiktokChallenges = createTool({
  id: "search-tiktok-challenges",
  description: "Search for TikTok challenges/hashtags",
  inputSchema: z.object({
    keyword: z.string(),
    offset: z.number().default(0),
    count: z.number().default(20),
  }),
  execute: async ({ context }) => {
    const { keyword, offset, count } = context;
    const challenges = await videoScouter.searchTiktokChallenges(
      keyword,
      offset,
      count
    );
    return challenges;
  },
});

/**
 * Tool for scouting TikTok hashtag videos and updating the database
 */
export const scoutTiktokHashtagVideos = createTool({
  id: "scout-tiktok-hashtag-videos",
  description: "Scout TikTok hashtag videos and update the database",
  inputSchema: z.object({
    challengeId: z.string(),
    cursor: z.number().default(0),
    count: z.number().default(20),
  }),
  execute: async ({ context }) => {
    const { challengeId, cursor, count } = context;
    const result = await videoScouter.scoutTiktokHashtagVideos(
      challengeId,
      cursor,
      count
    );
    return {
      videos: result.videos,
      processedCount: result.processedVideoIds.length,
      message: `Processed ${result.processedVideoIds.length} videos. ${result.videos.length - result.processedVideoIds.length} videos failed to process.`,
    };
  },
});
