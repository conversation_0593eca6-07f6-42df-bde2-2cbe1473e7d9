/**
 * Options for URL validation
 */
interface UrlValidationOptions {
  /** Allow protocols other than http/https (default: false) */
  allowCustomProtocols?: boolean;
  /** Require HTTPS only (default: false) */
  requireHttps?: boolean;
  /** Allow localhost URLs (default: true) */
  allowLocalhost?: boolean;
  /** Allow IP addresses (default: true) */
  allowIpAddresses?: boolean;
  /** Allowed protocols (default: ['http:', 'https:']) */
  allowedProtocols?: string[];
}

/**
 * Result of URL validation
 */
interface UrlValidationResult {
  isValid: boolean;
  url?: URL;
  error?: string;
}

/**
 * Check if a string is a valid URL
 * @param urlString - The URL string to validate
 * @param options - Validation options
 * @returns Validation result with parsed URL if valid
 */
function isValidUrl(
  urlString: string,
  options: UrlValidationOptions = {},
): UrlValidationResult {
  const {
    allowCustomProtocols = false,
    requireHttps = false,
    allowLocalhost = true,
    allowIpAddresses = true,
    allowedProtocols = ['http:', 'https:'],
  } = options;

  // Basic string validation
  if (!urlString || typeof urlString !== 'string') {
    return { isValid: false, error: 'URL must be a non-empty string' };
  }

  // Trim whitespace
  const trimmedUrl = urlString.trim();

  try {
    // Use URL constructor for parsing
    const url = new URL(trimmedUrl);

    // Protocol validation
    if (!allowCustomProtocols && !allowedProtocols.includes(url.protocol)) {
      return {
        isValid: false,
        error: `Protocol "${url.protocol}" not allowed. Allowed: ${allowedProtocols.join(', ')}`,
      };
    }

    // HTTPS requirement
    if (requireHttps && url.protocol !== 'https:') {
      return { isValid: false, error: 'HTTPS protocol required' };
    }

    // Localhost validation
    if (
      !allowLocalhost &&
      (url.hostname === 'localhost' ||
        url.hostname === '127.0.0.1' ||
        url.hostname === '0.0.0.0' ||
        url.hostname.endsWith('.localhost'))
    ) {
      return { isValid: false, error: 'Localhost URLs not allowed' };
    }

    // IP address validation
    if (!allowIpAddresses && isIpAddress(url.hostname)) {
      return { isValid: false, error: 'IP addresses not allowed' };
    }

    // Additional hostname validation
    if (!url.hostname) {
      return { isValid: false, error: 'Invalid hostname' };
    }

    return { isValid: true, url };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Invalid URL format',
    };
  }
}

/**
 * Simple check if hostname is an IP address
 */
function isIpAddress(hostname: string): boolean {
  // IPv4 pattern
  const ipv4Pattern = /^(\d{1,3}\.){3}\d{1,3}$/;

  // IPv6 pattern (simplified)
  const ipv6Pattern = /^([0-9a-f]{0,4}:){2,7}[0-9a-f]{0,4}$/i;

  return ipv4Pattern.test(hostname) || ipv6Pattern.test(hostname);
}

/**
 * Simple boolean check for valid URL
 * @param urlString - The URL string to validate
 * @param options - Validation options
 * @returns true if valid, false otherwise
 */
function isUrl(urlString: string, options?: UrlValidationOptions): boolean {
  return isValidUrl(urlString, options).isValid;
}

/**
 * Validate multiple URLs
 * @param urls - Array of URL strings to validate
 * @param options - Validation options
 * @returns Array of validation results
 */
function validateUrls(
  urls: string[],
  options?: UrlValidationOptions,
): UrlValidationResult[] {
  return urls.map((url) => isValidUrl(url, options));
}

export {
  isValidUrl,
  isUrl,
  validateUrls,
  type UrlValidationOptions,
  type UrlValidationResult,
};
