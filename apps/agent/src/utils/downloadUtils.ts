/**
 * Downloads an image from a URL and returns it as different DataContent types
 */

// Type definition for DataContent
type DataContent = string | Uint8Array | ArrayBuffer | Buffer;

// Enum for return type options
export enum DataContentType {
  BASE64 = 'base64',
  UINT8_ARRAY = 'uint8array',
  ARRAY_BUFFER = 'arraybuffer',
  BUFFER = 'buffer',
}

/**
 * Downloads an image and returns it as base64 string
 */
async function downloadImageAsBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        // Extract base64 data (remove data URL prefix if needed)
        const base64Data = base64.split(',')[1] || base64;
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    throw new Error(`Failed to download image: ${error}`);
  }
}

/**
 * Downloads an image and returns it as Uint8Array
 */
async function downloadImageAsUint8Array(url: string): Promise<Uint8Array> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  } catch (error) {
    throw new Error(`Failed to download image: ${error}`);
  }
}

/**
 * Downloads an image and returns it as ArrayBuffer
 */
async function downloadImageAsArrayBuffer(url: string): Promise<ArrayBuffer> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.arrayBuffer();
  } catch (error) {
    throw new Error(`Failed to download image: ${error}`);
  }
}

/**
 * Main function to download image in any DataContent format
 */
export async function downloadImage(
  url: string,
  returnType: DataContentType = DataContentType.BASE64,
): Promise<DataContent> {
  switch (returnType) {
    case DataContentType.BASE64:
      return downloadImageAsBase64(url);

    case DataContentType.UINT8_ARRAY:
      return downloadImageAsUint8Array(url);

    case DataContentType.ARRAY_BUFFER:
      return downloadImageAsArrayBuffer(url);

    case DataContentType.BUFFER:
      // In browser environment, return Uint8Array instead
      if (typeof Buffer === 'undefined') {
        console.warn('Buffer not available in browser, returning Uint8Array');
        return downloadImageAsUint8Array(url);
      }
      // For Node.js environment
      const arrayBuffer = await downloadImageAsArrayBuffer(url);
      return Buffer.from(arrayBuffer);

    default:
      throw new Error(`Unsupported return type: ${returnType}`);
  }
}
