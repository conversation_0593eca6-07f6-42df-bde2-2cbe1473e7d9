import { workflowNameService } from '@/services/workflow/workflowNameService';

/**
 * Utility function to schedule an asynchronous workflow name update
 * This should be called after creating a workflow to generate a descriptive name
 *
 * @param workflowRunId The workflow run ID (trace ID)
 * @param workflowType The type/name of the workflow (e.g., 'creatorScoutWorkflow')
 * @param inputParameters The parameters passed to the workflow
 * @param timestamp Optional timestamp when the workflow was created
 *
 * @example
 * ```typescript
 * // After creating a workflow
 * const { runId, start } = workflow.createRun();
 * await workflowDbService.linkWorkflowToUser(userId, runId, 'Creator Scout Workflow');
 *
 * // Schedule name update asynchronously
 * scheduleWorkflowNameUpdate(runId, 'creatorScoutWorkflow', inputParameters);
 *
 * // Start the workflow
 * const result = await start({ triggerData: inputParameters });
 * ```
 */
export function scheduleWorkflowNameUpdate(
  workflowRunId: string,
  workflowType: string,
  inputParameters: any,
  timestamp?: Date,
): void {
  workflowNameService.scheduleWorkflowNameUpdate(
    workflowRunId,
    workflowType,
    inputParameters,
    timestamp,
  );
}

/**
 * Utility function to generate a workflow name synchronously (with fallback)
 * This can be used when you need the name immediately
 *
 * @param workflowType The type/name of the workflow
 * @param inputParameters The parameters passed to the workflow
 * @param timestamp Optional timestamp when the workflow was created
 * @returns Promise that resolves to the generated workflow name and reasoning
 */
export async function generateWorkflowName(
  workflowType: string,
  inputParameters: any,
  timestamp?: Date,
): Promise<{ workflowName: string; reasoning?: string }> {
  return workflowNameService.generateWorkflowName(
    workflowType,
    inputParameters,
    timestamp,
  );
}

/**
 * Utility function to update a workflow name immediately (not scheduled)
 * This will wait for the AI generation to complete before returning
 *
 * @param workflowRunId The workflow run ID
 * @param workflowType The type of workflow
 * @param inputParameters The parameters used to create the workflow
 * @param timestamp Optional timestamp when the workflow was created
 */
export async function updateWorkflowNameNow(
  workflowRunId: string,
  workflowType: string,
  inputParameters: any,
  timestamp?: Date,
): Promise<void> {
  return workflowNameService.updateWorkflowNameAsync(
    workflowRunId,
    workflowType,
    inputParameters,
    timestamp,
  );
}
