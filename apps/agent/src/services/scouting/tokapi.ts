import axios, { AxiosError } from 'axios';
import { env } from '@/lib/env';
import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';
import { TiktokServiceSchema } from '@/schemas/tools_schema';

// Configuration for timeout behavior
interface RequestConfig {
  timeout: number;
}

// Default configuration
const DEFAULT_REQUEST_CONFIG: RequestConfig = {
  timeout: 30000, // 30 seconds
};

// Custom error types for better error identification
export class TokAPIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
  ) {
    super(message);
    this.name = 'TokAPIError';
  }
}

export class TokAPIRateLimitError extends TokAPIError {
  constructor(message: string = 'TokAPI: Rate limit exceeded') {
    super(message, 429);
    this.name = 'TokAPIRateLimitError';
  }
}

export class TokAPITimeoutError extends TokAPIError {
  constructor(message: string = 'TokAPI: Request timeout') {
    super(message, 408);
    this.name = 'TokAPITimeoutError';
  }
}

// Rate limiter state
interface RateLimiterState {
  requests: number[];
  windowStart: number;
}

export interface TokAPIHashtagSearchResponse {
  ad_info: Record<string, any>;
  challenge_list: Array<{
    challenge_info: {
      cha_name: string;
      cid: string;
      desc: string;
      user_count: number;
      view_count: number;
      share_info?: {
        share_url?: string;
      };
    };
    items: any;
    position: any;
  }>;
  cursor: number;
  has_more: number;
  status_code: number;
}

export interface TokAPIVideoSearchResponse {
  search_item_list: Array<{
    aweme_info: {
      aweme_id: string;
      desc: string;
      create_time: number;
      author: {
        uid: string;
        nickname: string;
        unique_id: string;
        sec_uid: string;
        region: string;
        language: string;
        signature: string;
        follower_count: number;
        aweme_count: number;
        avatar_larger: {
          url_list: string[];
        };
        create_time: number;
        user_tags: string;
        youtube_channel_id: string;
        ins_id: string;
        twitter_id: string;
      };
      statistics?: {
        digg_count: number;
        comment_count: number;
        share_count: number;
        play_count?: number;
      };
      video: {
        play_addr: {
          url_list: string[];
        };
        cover: {
          url_list: string[];
        };
        duration: number;
      };
      group_id: string;
    };
  }>;
  cursor: number;
  has_more: number;
  status_code: number;
}

export interface TokAPIHashtagVideoSearchResponse {
  aweme_list: Array<{
    aweme_id: string;
    desc: string;
    create_time: number;
    author: {
      uid: string;
      nickname: string;
      unique_id: string;
      sec_uid: string;
      region: string;
      language: string;
      signature: string;
      follower_count: number;
      aweme_count: number;
      avatar_larger: {
        url_list: string[];
      };
      create_time: number;
      user_tags: string;
      youtube_channel_id: string;
      ins_id: string;
      twitter_id: string;
    };
    statistics?: {
      digg_count: number;
      comment_count: number;
      share_count: number;
      play_count?: number;
    };
    video: {
      play_addr: {
        url_list: string[];
      };
      cover: {
        url_list: string[];
      };
      duration: number;
    };
    group_id: string;
  }>;
  cursor: number;
  has_more: number;
  status_code: number;
}

export class TokAPIService implements TiktokServiceSchema {
  private baseUrl = 'https://tokapi-mobile-version.p.rapidapi.com';
  private apiKey: string;
  private apiHost = 'tokapi-mobile-version.p.rapidapi.com';
  private requestConfig: RequestConfig;
  private rateLimiter: RateLimiterState;

  // Rate limiter configuration (requests per minute)
  private readonly RATE_LIMIT = 150;
  private readonly RATE_WINDOW = 60000; // 1 minute

  constructor(requestConfig: Partial<RequestConfig> = {}) {
    this.apiKey = env.RAPIDAPI_KEY;
    this.requestConfig = { ...DEFAULT_REQUEST_CONFIG, ...requestConfig };

    // Initialize rate limiter
    this.rateLimiter = {
      requests: [],
      windowStart: Date.now(),
    };
  }

  private getHeaders() {
    return {
      'x-rapidapi-host': this.apiHost,
      'x-rapidapi-key': this.apiKey,
    };
  }

  /**
   * Check rate limit
   */
  private checkRateLimit(): boolean {
    const now = Date.now();

    // Clean old requests outside the window
    this.rateLimiter.requests = this.rateLimiter.requests.filter(
      (timestamp) => now - timestamp < this.RATE_WINDOW,
    );

    if (this.rateLimiter.requests.length >= this.RATE_LIMIT) {
      return false;
    }

    this.rateLimiter.requests.push(now);
    return true;
  }

  /**
   * Create appropriate error from axios error
   */
  private createErrorFromAxiosError(error: AxiosError): TokAPIError {
    if (!error.response) {
      if (error.code === 'ECONNABORTED') {
        return new TokAPITimeoutError(
          `TokAPI: Request timeout after ${this.requestConfig.timeout}ms`,
        );
      }
      return new TokAPIError('TokAPI: Network connection failed');
    }

    const status = error.response.status;
    const responseData = error.response.data as any;
    const message =
      responseData?.message || responseData?.error || error.message;

    switch (status) {
      case 429:
        return new TokAPIRateLimitError(
          `TokAPI: Rate limit exceeded - ${message}`,
        );
      case 401:
        return new TokAPIError(
          'TokAPI: Unauthorized - Invalid API key',
          status,
        );
      case 403:
        return new TokAPIError('TokAPI: Forbidden - Access denied', status);
      case 408:
        return new TokAPITimeoutError(`TokAPI: Request timeout - ${message}`);
      case 500:
        return new TokAPIError('TokAPI: Internal server error', status);
      case 502:
      case 503:
      case 504:
        return new TokAPIError(
          'TokAPI: Service temporarily unavailable',
          status,
        );
      default:
        return new TokAPIError(`TokAPI: HTTP ${status} - ${message}`, status);
    }
  }

  /**
   * Execute HTTP request with rate limiting and proper error handling
   */
  private async executeRequest<T>(
    operation: string,
    requestFn: () => Promise<T>,
  ): Promise<T> {
    // Check rate limit before making request
    if (!this.checkRateLimit()) {
      throw new TokAPIRateLimitError(
        'TokAPI: Rate limit exceeded - too many requests',
      );
    }

    try {
      const result = await requestFn();
      return result;
    } catch (error) {
      // Convert axios errors to our custom errors for better error identification
      if (axios.isAxiosError(error)) {
        throw this.createErrorFromAxiosError(error);
      }

      // Re-throw non-axios errors as TokAPI errors
      throw new TokAPIError(
        `TokAPI: ${operation} failed - ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Search for videos on TikTok (direct)
   * @param keyword The hashtag to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   * @param sort_type Sort type (0: relevance, 1: most liked)
   * @param publish_time Publish time (0: unlimited, 1: last 24 hours, 7: past week, 30: past month, 90: past 3 months, 180: past 6 months)
   * @param region Region code (optional)
   */
  async searchVideos(
    keyword: string,
    offset = 0,
    count = 25,
    sort_type = 0,
    publish_time = 0,
    region?: string,
  ): Promise<TiktokVideoSchema[]> {
    const operation = `Search videos for "${keyword}"`;
    const sanitizedKeyword = keyword.replace(/#/g, '').trim();

    const response = await this.executeRequest(operation, async () => {
      return await axios.get<TokAPIVideoSearchResponse>(
        `${this.baseUrl}/v1/search/post`,
        {
          headers: this.getHeaders(),
          params: {
            keyword: sanitizedKeyword,
            offset,
            count,
            sort_type,
            publish_time,
            region,
          },
          timeout: this.requestConfig.timeout,
        },
      );
    });

    return response.data.search_item_list.map((video) => ({
      title: video.aweme_info.desc,
      hashtags: this.extractHashtags(video.aweme_info.desc),
      description: video.aweme_info.desc,
      platform: 'tiktok',
      video_id: video.aweme_info.aweme_id,
      video_url: video.aweme_info.video?.play_addr?.url_list?.[0] || '',
      thumbnail_url: video.aweme_info.video?.cover?.url_list?.[0] || '',
      publish_time: new Date(video.aweme_info.create_time * 1000).toISOString(),
      duration: video.aweme_info.video?.duration || 0,
      view_count: video.aweme_info.statistics?.play_count || 0,
      like_count: video.aweme_info.statistics?.digg_count || 0,
      comment_count: video.aweme_info.statistics?.comment_count || 0,
      share_count: video.aweme_info.statistics?.share_count || 0,
      author: {
        uid: video.aweme_info.author?.uid,
        nickname: video.aweme_info.author?.nickname,
        unique_id: video.aweme_info.author?.unique_id,
        sec_uid: video.aweme_info.author?.sec_uid,
        region: video.aweme_info.author?.region,
        language: video.aweme_info.author?.language,
        signature: video.aweme_info.author?.signature,
        aweme_count: video.aweme_info.author?.aweme_count,
        follower_count: video.aweme_info.author?.follower_count,
        avatar_url: video.aweme_info.author?.avatar_larger?.url_list?.[0] || '',
        create_time: video.aweme_info.author?.create_time,
        user_tags: video.aweme_info.author?.user_tags,
        youtube_channel_id: video.aweme_info.author?.youtube_channel_id,
        ins_id: video.aweme_info.author?.ins_id,
        twitter_id: video.aweme_info.author?.twitter_id,
      },
    }));
  }

  /**
   * Search for hashtags on TikTok
   * @param keyword The hashtag to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async searchHashtag(
    keyword: string,
    offset = 0,
    count = 10,
  ): Promise<TiktokChallengeSchema[]> {
    const operation = `Search hashtag for "${keyword}"`;

    const response = await this.executeRequest(operation, async () => {
      return await axios.get<TokAPIHashtagSearchResponse>(
        `${this.baseUrl}/v1/search/hashtag`,
        {
          headers: this.getHeaders(),
          params: { keyword, offset, count },
          timeout: this.requestConfig.timeout,
        },
      );
    });

    return response.data.challenge_list.map((challenge) => ({
      challenge_id: challenge.challenge_info.cid,
      challenge_name: challenge.challenge_info.cha_name,
      use_count: 0,
      user_count: challenge.challenge_info.user_count,
      view_count: challenge.challenge_info.view_count,
    }));
  }

  /**
   * Get videos for a specific creator
   * @param user_id The creator's user ID
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async getCreatorPosts(
    user_id: string,
    sec_user_id: string,
    offset = 0,
    count = 10,
  ): Promise<TiktokVideoSchema[]> {
    const operation = `Get creator posts for user_id "${user_id}"`;

    const response = await this.executeRequest(operation, async () => {
      return await axios.get<TokAPIHashtagVideoSearchResponse>(
        `${this.baseUrl}/v1/post/user/posts`,
        {
          headers: this.getHeaders(),
          params: { user_id, count, offset },
          timeout: this.requestConfig.timeout,
        },
      );
    });

    return response.data.aweme_list.map((video) => ({
      title: video.desc,
      hashtags: this.extractHashtags(video.desc),
      description: video.desc,
      platform: 'tiktok',
      video_id: video.aweme_id,
      video_url: video.video?.play_addr?.url_list?.[0] || '',
      thumbnail_url: video.video?.cover?.url_list?.[0] || '',
      publish_time: new Date(video.create_time * 1000).toISOString(),
      duration: video.video?.duration || 0,
      view_count: video.statistics?.play_count || 0,
      like_count: video.statistics?.digg_count || 0,
      comment_count: video.statistics?.comment_count || 0,
      share_count: video.statistics?.share_count || 0,
      author: {
        uid: video.author?.uid,
        nickname: video.author?.nickname,
        unique_id: video.author?.unique_id,
        sec_uid: video.author?.sec_uid,
        region: video.author?.region,
        language: video.author?.language,
        signature: video.author?.signature,
        aweme_count: video.author?.aweme_count,
        follower_count: video.author?.follower_count,
        avatar_url: video.author?.avatar_larger?.url_list?.[0] || '',
        create_time: video.author?.create_time,
        user_tags: video.author?.user_tags,
        youtube_channel_id: video.author?.youtube_channel_id,
        ins_id: video.author?.ins_id,
        twitter_id: video.author?.twitter_id,
      },
    }));
  }

  /**
   * Get videos for a specific hashtag
   * @param challengeId The challenge/hashtag ID
   * @param cursor Pagination cursor
   * @param count Number of results to return
   * @param region Region code (optional)
   */
  async getHashtagVideos(
    challengeId: string,
    cursor = 0,
    count = 10,
    region?: string,
  ): Promise<TiktokVideoSchema[]> {
    // Maximum items per page is 25
    const MAX_ITEMS_PER_PAGE = 25;
    let allVideos: TiktokVideoSchema[] = [];
    let currentCursor = cursor;
    let remainingCount = count;

    // Fetch pages until we have enough videos or there are no more results
    while (remainingCount > 0) {
      // Calculate how many items to fetch in this request
      const itemsToFetch = Math.min(remainingCount, MAX_ITEMS_PER_PAGE);
      const operation = `Get hashtag videos for challenge "${challengeId}" (cursor: ${currentCursor}, count: ${itemsToFetch})`;

      const response = await this.executeRequest(operation, async () => {
        return await axios.get<TokAPIHashtagVideoSearchResponse>(
          `${this.baseUrl}/v1/hashtag/posts/${challengeId}`,
          {
            headers: this.getHeaders(),
            params: {
              count: itemsToFetch,
              offset: currentCursor,
              region,
            },
            timeout: this.requestConfig.timeout,
          },
        );
      });

      const videos = response.data.aweme_list.map((video) => ({
        title: video.desc,
        hashtags: this.extractHashtags(video.desc),
        description: video.desc,
        platform: 'tiktok',
        video_id: video.aweme_id,
        video_url: video.video?.play_addr?.url_list?.[0] || '',
        thumbnail_url: video.video?.cover?.url_list?.[0] || '',
        publish_time: new Date(video.create_time * 1000).toISOString(),
        duration: video.video?.duration || 0,
        view_count: video.statistics?.play_count || 0,
        like_count: video.statistics?.digg_count || 0,
        comment_count: video.statistics?.comment_count || 0,
        share_count: video.statistics?.share_count || 0,
        author: {
          uid: video.author?.uid,
          nickname: video.author?.nickname,
          unique_id: video.author?.unique_id,
          sec_uid: video.author?.sec_uid,
          region: video.author?.region,
          language: video.author?.language,
          signature: video.author?.signature,
          aweme_count: video.author?.aweme_count,
          follower_count: video.author?.follower_count,
          avatar_url: video.author?.avatar_larger?.url_list?.[0] || '',
          create_time: video.author?.create_time,
          user_tags: video.author?.user_tags,
          youtube_channel_id: video.author?.youtube_channel_id,
          ins_id: video.author?.ins_id,
          twitter_id: video.author?.twitter_id,
        },
      }));

      allVideos = [...allVideos, ...videos];

      // Update remaining count
      remainingCount -= videos.length;

      // If we didn't get as many videos as requested or there are no more results, break
      if (videos.length < itemsToFetch || !response.data.has_more) {
        break;
      }

      // Update cursor for next page
      currentCursor = response.data.cursor;
    }

    return allVideos;
  }

  /**
   * Extract hashtags from a string
   * @param text Text containing hashtags
   */
  private extractHashtags(text: string): string[] {
    const hashtagRegex = /#(\w+)/g;
    const matches = text.match(hashtagRegex);
    return matches ? matches.map((tag) => tag.substring(1)) : [];
  }

  /**
   * Get current service health status
   */
  getHealthStatus() {
    return {
      serviceName: 'TokAPI',
      rateLimiter: {
        requestsInWindow: this.rateLimiter.requests.length,
        limit: this.RATE_LIMIT,
        windowStart: this.rateLimiter.windowStart,
      },
      config: {
        timeout: this.requestConfig.timeout,
      },
    };
  }

  /**
   * Update request configuration
   */
  updateRequestConfig(newConfig: Partial<RequestConfig>): void {
    this.requestConfig = { ...this.requestConfig, ...newConfig };
    console.log('[TokAPI] Request configuration updated:', this.requestConfig);
  }

  /**
   * Perform a health check by making a simple API call
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Make a simple search request to test connectivity
      await this.searchHashtag('test', 0, 1);
      return true;
    } catch (error) {
      console.error('[TokAPI] Health check failed:', error);
      return false;
    }
  }
}
