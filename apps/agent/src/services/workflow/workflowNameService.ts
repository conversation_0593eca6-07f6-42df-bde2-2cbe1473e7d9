import { workflowNameGenerator } from '@/agents/workflowNameGeneratorAgent';
import { workflowDbService } from '../index';
import { extractFirstJson } from '@/lib/utils';
import { z } from 'zod';

// Schema for the workflow name generation response
const workflowNameResponseSchema = z.object({
  workflowName: z.string(),
  reasoning: z.string().optional(),
});

/**
 * Service for generating and updating workflow names using AI
 */
export class WorkflowNameService {
  /**
   * Generate a descriptive name for a workflow based on its type and parameters
   * @param workflowType The type/name of the workflow
   * @param inputParameters The parameters passed to the workflow
   * @param timestamp Optional timestamp when the workflow was created
   * @returns Generated workflow name and reasoning
   */
  async generateWorkflowName(
    workflowType: string,
    inputParameters: any,
    timestamp?: Date,
  ): Promise<{ workflowName: string; reasoning?: string }> {
    try {
      // Prepare the input for the agent
      const agentInput = {
        workflowType,
        inputParameters,
        timestamp: timestamp?.toISOString() || new Date().toISOString(),
      };

      // Generate the workflow name using the AI agent
      const response = await workflowNameGenerator.generate([
        {
          role: 'user',
          content: JSON.stringify(agentInput, null, 2),
        },
      ]);

      // Extract the response content
      const assistantMessage = response.response.messages[0];
      const content = Array.isArray(assistantMessage.content)
        ? assistantMessage.content[0]
        : assistantMessage.content;

      const responseText =
        typeof content === 'string' ? content : (content as any).text;

      // Parse the JSON response
      const parsedResult = extractFirstJson(responseText);
      const validatedResult = workflowNameResponseSchema.parse(parsedResult);

      return {
        workflowName: validatedResult.workflowName,
        reasoning: validatedResult.reasoning,
      };
    } catch (error) {
      console.error('Error generating workflow name:', error);

      // Fallback to a generic name based on workflow type and timestamp
      const fallbackName = this.generateFallbackName(
        workflowType,
        inputParameters,
      );
      return {
        workflowName: fallbackName,
        reasoning: 'Generated using fallback logic due to AI generation error',
      };
    }
  }

  /**
   * Generate a fallback name when AI generation fails
   * @param workflowType The type of workflow
   * @param inputParameters The workflow parameters
   * @returns A fallback workflow name
   */
  private generateFallbackName(
    workflowType: string,
    inputParameters: any,
  ): string {
    const timestamp = new Date().toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    // Extract key information from parameters for common workflow types
    if (workflowType === 'creatorScoutWorkflow') {
      const description = inputParameters?.targetCreatorDescription;
      if (description) {
        // Extract key terms from description
        const keyTerms = this.extractKeyTerms(description);
        if (keyTerms.length > 0) {
          return `Scout ${keyTerms.slice(0, 2).join(' ')} Creators`;
        }
      }
      return `Creator Scout - ${timestamp}`;
    }

    // Generic fallback
    const workflowDisplayName = workflowType
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();

    return `${workflowDisplayName} - ${timestamp}`;
  }

  /**
   * Extract key terms from a description for naming
   * @param description The description text
   * @returns Array of key terms
   */
  private extractKeyTerms(description: string): string[] {
    // Simple keyword extraction - look for important terms
    const keywords = [
      'gaming',
      'fashion',
      'beauty',
      'food',
      'travel',
      'tech',
      'fitness',
      'music',
      'dance',
      'comedy',
      'education',
      'lifestyle',
      'business',
      'mobile',
      'console',
      'pc',
      'indie',
      'aaa',
      'casual',
      'influencer',
      'creator',
      'streamer',
      'youtuber',
      'tiktoker',
      'asia',
      'europe',
      'america',
      'global',
      'local',
      'regional',
    ];

    const lowerDescription = description.toLowerCase();
    const foundKeywords = keywords.filter((keyword) =>
      lowerDescription.includes(keyword),
    );

    // Also extract capitalized words (likely proper nouns or important terms)
    const capitalizedWords = description.match(/\b[A-Z][a-z]+\b/g) || [];

    return [...foundKeywords, ...capitalizedWords.map((w) => w.toLowerCase())]
      .filter((term, index, arr) => arr.indexOf(term) === index) // Remove duplicates
      .slice(0, 3); // Limit to 3 terms
  }

  /**
   * Update a workflow's name asynchronously after it has been created
   * @param workflowRunId The workflow run ID
   * @param workflowType The type of workflow
   * @param inputParameters The parameters used to create the workflow
   * @param timestamp Optional timestamp when the workflow was created
   */
  async updateWorkflowNameAsync(
    workflowRunId: string,
    workflowType: string,
    inputParameters: any,
    timestamp?: Date,
  ): Promise<void> {
    try {
      console.log(`🏷️ Generating name for workflow ${workflowRunId}...`);

      // Generate the workflow name
      const { workflowName, reasoning } = await this.generateWorkflowName(
        workflowType,
        inputParameters,
        timestamp,
      );

      // Update the workflow name in the database
      await (workflowDbService as any).updateWorkflowName(
        workflowRunId,
        workflowName,
      );

      console.log(
        `✅ Updated workflow ${workflowRunId} name to: "${workflowName}"`,
      );
      if (reasoning) {
        console.log(`💡 Reasoning: ${reasoning}`);
      }
    } catch (error) {
      console.error(
        `❌ Failed to update workflow name for ${workflowRunId}:`,
        error,
      );
      // Don't throw the error - this is a non-critical background operation
    }
  }

  /**
   * Schedule an asynchronous workflow name update (fire and forget)
   * @param workflowRunId The workflow run ID
   * @param workflowType The type of workflow
   * @param inputParameters The parameters used to create the workflow
   * @param timestamp Optional timestamp when the workflow was created
   */
  scheduleWorkflowNameUpdate(
    workflowRunId: string,
    workflowType: string,
    inputParameters: any,
    timestamp?: Date,
  ): void {
    // Use setImmediate to schedule the update for the next tick of the event loop
    setImmediate(() => {
      this.updateWorkflowNameAsync(
        workflowRunId,
        workflowType,
        inputParameters,
        timestamp,
      ).catch((error) => {
        console.error(
          `Background workflow name update failed for ${workflowRunId}:`,
          error,
        );
      });
    });
  }
}

// Export a singleton instance
export const workflowNameService = new WorkflowNameService();
