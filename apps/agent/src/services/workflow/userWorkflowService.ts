import { WorkflowNames } from '@repo/constants';
import { mastra } from '@/mastra';
import { workflowDbService } from '../index';
import { scheduleWorkflowNameUpdate } from '@/utils/workflowNameUpdater';

/**
 * Service for managing user workflows
 */
export class UserWorkflowService {
  /**
   * Create a new workflow run and link it to a user
   * @param userId The user ID
   * @param workflowName The name of the workflow to run
   * @param triggerData The data to trigger the workflow with
   * @returns The workflow run ID and start function
   */
  async createWorkflowRun(
    userId: string,
    workflowName: WorkflowNames,
    triggerData: any,
  ) {
    // Get the workflow from Mastra
    const workflow = mastra.getWorkflow(workflowName);
    if (!workflow) {
      throw new Error(`Workflow ${workflowName} not found`);
    }

    // Create a new run
    const { runId, start } = workflow.createRun();
    const createdAt = new Date();

    // Link the workflow to the user with a temporary name
    const tempName = `${workflowName} - ${createdAt.toLocaleString()}`;
    await workflowDbService.linkWorkflowToUser(userId, runId, tempName);

    // Schedule asynchronous name generation based on workflow type and parameters
    scheduleWorkflowNameUpdate(runId, workflowName, triggerData, createdAt);

    return {
      runId,
      start: async () => {
        try {
          // Start the workflow
          const result = await start({ inputData: triggerData });

          // Update the workflow status
          await workflowDbService.updateWorkflowStatus(runId, 'completed');

          return result;
        } catch (error) {
          // Update the workflow status to failed
          await workflowDbService.updateWorkflowStatus(runId, 'failed');
          throw error;
        }
      },
    };
  }

  /**
   * Get all workflows for a user
   * @param userId The user ID
   * @returns Array of user workflow records
   */
  async getUserWorkflows(userId: string) {
    return await workflowDbService.getUserWorkflows(userId);
  }

  /**
   * Get a specific workflow by run ID
   * @param workflowRunId The workflow run ID
   * @returns The workflow record or null if not found
   */
  async getWorkflowByRunId(workflowRunId: string) {
    return await workflowDbService.getWorkflowByRunId(workflowRunId);
  }

  /**
   * Get workflow context data for a specific workflow run
   * @param workflowRunId The workflow run ID
   * @returns The workflow context data
   */
  async getWorkflowContexts(workflowRunId: string) {
    return await workflowDbService.getAllWorkflowContexts(workflowRunId);
  }
}

// Export a singleton instance
export const userWorkflowService = new UserWorkflowService();
