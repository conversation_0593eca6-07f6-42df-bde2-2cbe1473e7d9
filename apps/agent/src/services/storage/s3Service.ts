import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { env } from '@/lib/env';
import axios from 'axios';
import { createHash } from 'crypto';
import { extname } from 'path';

/**
 * Configuration for S3 OSS service
 */
export const S3_CONFIG = {
  DEFAULT_MAX_CONCURRENT_UPLOADS: 10,
  UPLOAD_TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

/**
 * Interface for upload result
 */
export interface UploadResult {
  originalUrl: string;
  s3Url: string;
  key: string;
  success: boolean;
  error?: string;
}

/**
 * Interface for file upload data
 */
export interface FileUploadData {
  url: string;
  type: 'thumbnail' | 'avatar' | 'video';
  metadata?: Record<string, string>;
}

/**
 * S3 OSS Service for uploading and managing files
 */
export class S3Service {
  private s3Client: S3Client;
  private bucketName: string;
  private maxConcurrentUploads: number;

  constructor() {
    // Initialize S3 client with configuration
    const s3Config: any = {
      region: env.S3_REGION,
      credentials: {
        accessKeyId: env.S3_ACCESS_KEY_ID,
        secretAccessKey: env.S3_SECRET_ACCESS_KEY,
      },
    };

    // Add custom endpoint if provided (for OSS compatibility)
    if (env.S3_ENDPOINT) {
      s3Config.endpoint = env.S3_ENDPOINT;
    }

    this.s3Client = new S3Client(s3Config);
    this.bucketName = env.S3_BUCKET_NAME;
    this.maxConcurrentUploads = env.MAX_UPLOAD_CONCURRENT_JOBS
      ? parseInt(env.MAX_UPLOAD_CONCURRENT_JOBS)
      : S3_CONFIG.DEFAULT_MAX_CONCURRENT_UPLOADS;

    console.log(
      `S3 Service initialized with bucket: ${this.bucketName}, max concurrent uploads: ${this.maxConcurrentUploads}`,
    );
  }

  /**
   * Generate a unique S3 key for a file
   * @param originFileName Original name of the file
   * @param type Type of file (thumbnail, avatar, video)
   * @returns Generated S3 key
   */
  private generateS3KeyByName(originFileName: string, type: string): string {
    const key = `media/${type}/${originFileName}`;
    return key;
  }

  /**
   * Generate a unique S3 key for a file
   * @param originalUrl Original URL of the file
   * @param type Type of file (thumbnail, avatar, video)
   * @returns Generated S3 key
   */
  private generateS3Key(originalUrl: string, type: string): string {
    // Create a hash of the original URL for uniqueness
    const urlHash = createHash('md5').update(originalUrl).digest('hex');

    // Generate key with timestamp and hash
    const timestamp = Date.now();
    const key = `media/${type}/${urlHash}-${timestamp}`;

    return key;
  }

  /**
   * Extract file extension from URL
   * @param url File URL
   * @returns File extension with dot (e.g., '.jpg')
   */
  private extractFileExtension(url: string): string {
    try {
      // Remove query parameters and fragments
      const cleanUrl = url.split('?')[0].split('#')[0];
      const extension = extname(cleanUrl);

      // Default to .jpg if no extension found
      return extension || '.jpg';
    } catch (error) {
      console.warn(`Failed to extract extension from URL: ${url}`, error);
      return '.jpg';
    }
  }

  /**
   * Download file from URL with retry logic
   * @param url File URL to download
   * @returns Buffer containing file data
   */
  private async downloadFile(url: string): Promise<Buffer> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= S3_CONFIG.RETRY_ATTEMPTS; attempt++) {
      try {
        // console.log(
        //   `Downloading file (attempt ${attempt}/${S3_CONFIG.RETRY_ATTEMPTS}): ${url}`,
        // );

        const response = await axios.get(url, {
          responseType: 'arraybuffer',
          timeout: S3_CONFIG.UPLOAD_TIMEOUT,
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
        });

        return Buffer.from(response.data);
      } catch (error) {
        lastError = error as Error;
        console.warn(`Download attempt ${attempt} failed for ${url}:`, error);

        if (attempt < S3_CONFIG.RETRY_ATTEMPTS) {
          await this.delay(S3_CONFIG.RETRY_DELAY * attempt);
        }
      }
    }

    throw new Error(
      `Failed to download file after ${S3_CONFIG.RETRY_ATTEMPTS} attempts: ${lastError?.message}`,
    );
  }

  /**
   * Upload file buffer to S3
   * @param buffer File buffer
   * @param key S3 key
   * @param contentType Content type
   * @returns S3 URL of uploaded file
   */
  private async uploadToS3(
    buffer: Buffer,
    key: string,
    contentType: string,
  ): Promise<string> {
    try {
      const putCmd = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        ACL: 'public-read', // TODO: Make files publicly accessible for now
      });

      await this.s3Client.send(putCmd);

      // Generate public URL
      const s3Url = env.S3_ENDPOINT
        ? `${env.S3_ENDPOINT}/${this.bucketName}/${key}`
        : `https://${this.bucketName}.s3.${env.S3_REGION}.amazonaws.com/${key}`;

      // console.log(`Successfully uploaded to S3: ${s3Url}`);
      return s3Url;
    } catch (error) {
      console.error(`Failed to upload to S3 with key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Determine content type from file extension
   * @param extension File extension
   * @returns MIME type
   */
  private getContentType(extension: string): string {
    const contentTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.avi': 'video/x-msvideo',
      '.webm': 'video/webm',
    };

    return contentTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Upload a single file from URL to S3
   * @param fileData File upload data
   * @returns Upload result
   */
  async uploadFile(fileData: FileUploadData): Promise<UploadResult> {
    const { url, type } = fileData;

    try {
      // console.log(`Starting upload for ${type}: ${url}`);

      // Generate S3 key
      const key = this.generateS3Key(url, type);

      // Download file
      const buffer = await this.downloadFile(url);

      // Determine content type
      const extension = this.extractFileExtension(url);
      const contentType = this.getContentType(extension);

      // Upload to S3
      const s3Url = await this.uploadToS3(buffer, key, contentType);

      return {
        originalUrl: url,
        s3Url,
        key,
        success: true,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      console.error(`Failed to upload ${type} from ${url}:`, errorMessage);

      return {
        originalUrl: url,
        s3Url: '',
        key: '',
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Upload multiple files in parallel with concurrency control
   * @param files Array of file upload data
   * @returns Array of upload results
   */
  async uploadFilesInParallel(
    files: FileUploadData[],
  ): Promise<UploadResult[]> {
    console.log(
      `Starting parallel upload of ${files.length} files with max concurrency: ${this.maxConcurrentUploads}`,
    );

    const results: UploadResult[] = [];

    // Process files in batches to control concurrency
    for (let i = 0; i < files.length; i += this.maxConcurrentUploads) {
      const batch = files.slice(i, i + this.maxConcurrentUploads);
      const batchNumber = Math.floor(i / this.maxConcurrentUploads) + 1;
      const totalBatches = Math.ceil(files.length / this.maxConcurrentUploads);

      console.log(
        `Processing upload batch ${batchNumber}/${totalBatches} with ${batch.length} files`,
      );

      // Upload batch in parallel
      const batchPromises = batch.map((file) => this.uploadFile(file));
      const batchResults = await Promise.allSettled(batchPromises);

      // Process results
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error('Upload promise rejected:', result.reason);
          // Create a failed result for rejected promises
          results.push({
            originalUrl: 'unknown',
            s3Url: '',
            key: '',
            success: false,
            error: result.reason?.message || 'Promise rejected',
          });
        }
      }

      console.log(
        `Batch ${batchNumber} completed. Success: ${results.filter((r) => r.success).length}/${results.length}`,
      );
    }

    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.length - successCount;

    console.log(
      `Parallel upload completed. Success: ${successCount}, Failed: ${failureCount}`,
    );

    return results;
  }

  /**
   * Utility function to delay execution
   * @param ms Milliseconds to delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get service configuration info
   * @returns Configuration information
   */
  getServiceInfo() {
    return {
      bucketName: this.bucketName,
      region: env.S3_REGION,
      endpoint: env.S3_ENDPOINT || 'default',
      maxConcurrentUploads: this.maxConcurrentUploads,
    };
  }
}

// Export singleton instance
export const s3Service = new S3Service();
