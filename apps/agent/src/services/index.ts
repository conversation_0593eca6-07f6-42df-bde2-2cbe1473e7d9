import { env } from '@/lib/env';
import { createDb } from '@repo/db/client';
import {
  createScoutDbService,
  createWorkflowDbService,
} from '@repo/db/operations';

const db = createDb({
  databaseUrl: env.DB_POSTGRES_URL,
});

export const scoutDbService = createScoutDbService(db);
export const workflowDbService = createWorkflowDbService(db);

// Export S3 service
export { s3Service } from './storage/s3Service';
