import { CoreMessage, createStep, createWorkflow } from '@mastra/core';
// import { Step, Workflow } from '@mastra/core/workflows';
// import { LegacyStep, LegacyWorkflow } from '@mastra/core/workflows/legacy';

import { z } from 'zod';
import { videoScouter } from '@/services/scouting/videoScouter';
import { WorkflowNames } from '@repo/constants';

const videoSearchSchema = z.object({
  keywords: z.array(z.string()),
  keywordCount: z.number(),
});

const challengSearchResultSchema = z.object({
  challengeCount: z.number(),
  totalChallengesFound: z.number(),
  duplicatesRemoved: z.number(),
  keywordsProcessed: z.number(),
});

export const aiScriptWorkflow = createWorkflow({
  id: WorkflowNames.aiScriptWorkflow,
  inputSchema: z.object({
    campaignDescription: z.string(),
  }),
  outputSchema: z.object({
    videoCount: z.number(),
    processedCount: z.number(),
    totalVideosFetched: z.number(),
    videosFiltered: z.number(),
    challengesProcessed: z.number(),
    averageVideosPerChallenge: z.number(),
  }),
});

// const runId = '834e26e2-3f1e-4fb2-9615-c0e77c22fcb3';

const stepOne = createStep({
  id: 'analyze-description',
  inputSchema: aiScriptWorkflow.inputSchema,
  outputSchema: videoSearchSchema,
  execute: async ({ inputData, mastra }) => {
    const description = inputData.campaignDescription;
    console.log('description', description);

    const campaignAnalyzer = mastra?.getAgent('campaignAnalyzer');
    if (!campaignAnalyzer) {
      throw new Error('Campaign analyzer agent not found');
    }

    const userDescription: CoreMessage = {
      role: 'user',
      content: description,
    };

    console.log('userDescription', userDescription);

    const resp = await campaignAnalyzer.generate([userDescription], {
      output: z.object({
        keywords: z.array(z.string()),
      }),
    });

    console.log('resp', resp);

    return {
      keywords: resp.object.keywords,
      keywordCount: resp.object.keywords.length,
    };
  },
});

const stepTwo = createStep({
  id: 'find-best-challenges',
  inputSchema: videoSearchSchema,
  outputSchema: challengSearchResultSchema,
  execute: async ({ inputData, runId }) => {
    const { keywords, keywordCount } = inputData;
    console.log('Finding best challenges for keywords:', keywords);

    // Track metrics during the process
    let totalChallengesFound = 0;
    let duplicatesRemoved = 0;

    // We need to modify the findBestChallenges method to return more metrics
    // For now, let's implement the logic here to get the metrics
    const allChallenges: any[] = [];

    // Search for challenges for each keyword
    for (const keyword of keywords) {
      try {
        const challenges = await videoScouter.searchTiktokChallenges(
          keyword,
          0,
          20,
        );
        console.log(
          `Found ${challenges.length} challenges for keyword "${keyword}"`,
        );
        totalChallengesFound += challenges.length;
        allChallenges.push(...challenges);
      } catch (error) {
        console.error(
          `Error searching challenges for keyword "${keyword}":`,
          error,
        );
      }
    }

    // Calculate duplicates removed
    const uniqueChallenges = allChallenges.filter(
      (challenge, index, self) =>
        index ===
        self.findIndex((c) => c.challenge_id === challenge.challenge_id),
    );
    duplicatesRemoved = allChallenges.length - uniqueChallenges.length;

    // Sort and take the best ones
    const sortedChallenges = uniqueChallenges.sort(
      (a, b) => b.view_count - a.view_count,
    );
    const bestChallenges = sortedChallenges.slice(
      0,
      Math.max(20, sortedChallenges.length),
    );

    // Save to context (similar to what findBestChallenges does)
    const contextData = {
      keywords,
      challenges: bestChallenges,
      timestamp: new Date().toISOString(),
    };

    // We'll need to import workflowDbService
    const { workflowDbService } = await import('@/services/index');
    await workflowDbService.saveWorkflowContext(
      runId,
      'best_challenges',
      contextData,
    );

    return {
      challengeCount: bestChallenges.length,
      totalChallengesFound,
      duplicatesRemoved,
      keywordsProcessed: keywordCount,
    };
  },
});

const stepThree = createStep({
  id: 'collect-good-videos',
  inputSchema: challengSearchResultSchema,
  outputSchema: aiScriptWorkflow.outputSchema,
  execute: async ({ inputData, runId }) => {
    console.log('Collecting good videos from challenges');

    const { challengeCount } = inputData;

    // Get challenges from context to track more detailed metrics
    const { workflowDbService } = await import('@/services/index');
    const contextData = await workflowDbService.getWorkflowContext(
      runId,
      'best_challenges',
    );

    if (!contextData || !contextData.contextData) {
      throw new Error(
        'No challenges found in context. Run findBestChallenges first.',
      );
    }

    const contextDataObj = contextData.contextData as {
      challenges: any[];
    };

    const challenges = contextDataObj.challenges;
    let totalVideosFetched = challenges.length * 25;
    let challengesProcessed = 0;

    // Track videos collected from each challenge
    const allVideos: any[] = [];
    const processedVideoIds: number[] = [];

    // Fetch videos from each challenge until we have enough
    for (const challenge of challenges) {
      if (allVideos.length >= 100) {
        break;
      }

      try {
        console.log(
          `Fetching videos for challenge: ${challenge.challenge_name}`,
        );

        // Get videos for this challenge
        const result = await videoScouter.scoutTiktokHashtagVideos(
          challenge.challenge_id,
          0,
          25,
        );

        // totalVideosFetched += result.videos.length;
        allVideos.push(...result.videos);
        processedVideoIds.push(...result.processedVideoIds);
        challengesProcessed++;

        console.log(
          `Collected ${allVideos.length}/100 videos so far from ${challengesProcessed} challenges`,
        );
      } catch (error) {
        console.error(
          `Error fetching videos for challenge ${challenge.challenge_name}:`,
          error,
        );
      }
    }

    // Sort videos by view count (descending) - this is the "filtering"
    const sortedVideos = allVideos.sort((a, b) => b.view_count - a.view_count);
    const finalVideoCount = sortedVideos.length;
    const videosFiltered = totalVideosFetched - finalVideoCount; // Videos that were filtered out during sorting/deduplication

    // Calculate average videos per challenge
    const averageVideosPerChallenge =
      challengesProcessed > 0
        ? Math.round((totalVideosFetched / challengesProcessed) * 100) / 100
        : 0;

    // Save to context (similar to what collectGoodVideos does)
    const contextData2 = {
      challenges,
      videos: sortedVideos.map((video) => ({
        video_id: video.video_id,
        title: video.title,
        view_count: video.view_count,
        like_count: video.like_count,
        comment_count: video.comment_count,
        author: {
          unique_id: video.author.unique_id,
          nickname: video.author.nickname,
        },
      })),
      processedVideoIds,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      runId,
      'good_videos',
      contextData2,
    );

    console.log('Final metrics:', {
      videoCount: finalVideoCount,
      processedCount: processedVideoIds.length,
      totalVideosFetched,
      videosFiltered,
      challengesProcessed,
      averageVideosPerChallenge,
    });

    return {
      videoCount: finalVideoCount,
      processedCount: processedVideoIds.length,
      totalVideosFetched,
      videosFiltered,
      challengesProcessed,
      averageVideosPerChallenge,
    };
  },
});

aiScriptWorkflow.then(stepOne).then(stepTwo).then(stepThree).commit();
