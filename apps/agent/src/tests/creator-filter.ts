import {
  creator<PERSON><PERSON>er<PERSON><PERSON>,
  CreatorFilterOutputSchema,
} from '@/agents/creatorFilterAgent';
import { <PERSON><PERSON> } from '@mastra/core';

// Register the workflow
const mastra = new Mastra({
  agents: { creatorFilterAgent },
});

(async () => {
  const agent = mastra.getAgent('creatorFilterAgent');
  const resp = await agent.generate(
    [
      {
        role: 'user',
        content: `Filter Mode: STRICT\nUser Specifications: I need to find some American gaming creators with following rules: 1. They post videos about various games, not just one game. 2. The median view count of their recent videos must greater than 50k. 3. They speak English. 4. They show their faces and have voiceovers in their videos.\n\nExtra Scout Guidance: ## STEP 1: ANALYZE REQUIREMENTS\n\n**AUDIENCE_LANG**: English  \n**CREATOR_ORIGIN**: American  \n**MARKET_LOCATION**: USA  \n**CREATOR_ACTIVITY**: Gaming con…mentary", "#gamerreacts", "#gamereviews", "#tryingnewgames", "#gamingsetup", "#indiegames", "#gamepass", "#steamgames", "#gamingcreator", "#gamenight"],\n  "adjacent": ["#gaming", "#gamer", "#twitch", "#youtube", "#pcgaming", "#consolegaming", "#gamedev", "#esports"],\n  "reason": "These tags capture variety gamers who showcase personality through face-cam and commentary, distinguishing them from single-game specialists or faceless content creators while targeting engagement-focused creators."\n}`,
      },
      {
        role: 'user',
        content: [
          {
            type: 'image',
            image: new URL(
              'https://fastly.picsum.photos/id/237/200/300.jpg?hmac=TmmQSbShHz9CdQm0NkEjx1Dyh_Y984R9LpNrpvH2D_U',
            ),
          },
          {
            type: 'text',
            text: 'Random text',
          },
        ],
      },
    ],
    {
      output: CreatorFilterOutputSchema,
    },
  );

  console.log('resp', resp);
})();
