import { videoScouter } from "../services/scouting/videoScouter";

/**
 * Test script for the video scouter service
 */
async function main() {
  try {
    // 1. Search for TikTok challenges
    console.log("Searching for TikTok challenges...");
    const challenges = await videoScouter.searchTiktokChallenges("gaming");
    console.log(`Found ${challenges.length} challenges`);

    if (challenges.length > 0) {
      const challenge = challenges[0];
      console.log(
        `Selected first challenge: ${challenge.challenge_name} (ID: ${challenge.challenge_id})`
      );

      // 2. Scout videos for the first challenge
      console.log(
        `Scouting videos for challenge ${challenge.challenge_name}...`
      );
      const result = await videoScouter.scoutTiktokHashtagVideos(
        challenge.challenge_id,
        0,
        5
      );

      console.log(`Found ${result.videos.length} videos`);
      console.log(
        `Successfully processed ${result.processedVideoIds.length} videos`
      );

      // 3. Print some details about the videos
      result.videos.forEach((video, index) => {
        console.log(
          `Video ${index + 1}: ${video.title.substring(0, 30)}... (${video.video_id})`
        );
        console.log(
          `  Views: ${video.view_count}, Likes: ${video.like_count}, Comments: ${video.comment_count}`
        );
        console.log(
          `  Creator: ${video.author.nickname} (${video.author.unique_id})`
        );
        console.log(`  Hashtags: ${video.hashtags.join(", ")}`);
        console.log("---");
      });
    }
  } catch (error) {
    console.error("Error:", error);
  }
}

// Run the test
main().catch(console.error);
