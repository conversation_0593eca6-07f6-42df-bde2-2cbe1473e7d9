import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';
import { Mastra } from '@mastra/core';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

/**
 * Load existing KOLs from file
 */
function loadExistingKols(): string[] {
  const kolsFilePath = join(process.cwd(), 'src/tests/existing_kols.txt');

  if (!existsSync(kolsFilePath)) {
    console.warn(
      `⚠️ existing_kols.txt not found at ${kolsFilePath}, proceeding without existing KOL exclusions`,
    );
    return [];
  }

  try {
    const fileContent = readFileSync(kolsFilePath, 'utf8');
    const kolIds = fileContent
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0); // Filter out empty lines

    console.log(
      `📋 Loaded ${kolIds.length} existing KOL IDs from ${kolsFilePath}`,
    );
    console.log(`🚫 These KOLs will be excluded from new scouting results`);
    return kolIds;
  } catch (error) {
    console.error(
      `❌ Failed to load existing KOLs from ${kolsFilePath}:`,
      error,
    );
    return [];
  }
}

// Register the workflow
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: { scoutCreaotrWithChallengeWorkflow: creatorScoutWorkflow },
});

/**
 * Run Scout Campaign demo (new functionality)
 */
async function runScoutCampaignDemo() {
  console.log('🎯 Starting Scout Campaign Demo...\n');

  const campaignConfig: CampaignConfig = {
    campaignId: 'japanese-kols-demo-#9',
    campaignName: 'Japanese KOLs Demo Campaign #9',
    description: 'Find Japanese KOLs for Ctrip',

    targetKOLCount: 1200,
    kolPerTask: 100,
    maxWorkflowRuns: 40,

    sharedConfig: {
      targetCreatorDescription: `Identify authentic Japanese content creators meeting these specific criteria:

CORE REQUIREMENTS:
✓ Japanese Creator Verification:
  - Primary: Native Japanese language usage in titles/descriptions (hiragana, katakana, kanji)
  - Secondary: Visual confirmation of Japanese ethnicity in content
  - Note: Disregard platform metadata (region/language settings) as these are often inaccurate
  - Non-Japanese creators must be excluded immediately

✓ Audience Size: 2,000 - 20,000 followers (strict range)

✓ Content Validation (Thumbnail Analysis):
  - Must include outdoor/location-based content (minimum 1 thumbnail)
  - Must feature creator's face visible (minimum 1 thumbnail)

CONTENT PARAMETERS:
- Genre: All Japanese content types welcome (lifestyle, landmarks, vlogs, camping, travel, cultural content, transportation, local experiences, etc.)
- Quality indicators: Regular posting schedule, authentic Japanese perspectives, engaged audience
- Language: Content primarily in Japanese for local audience

SCREENING SEQUENCE:
1. Japanese language authentication (elimination filter)
2. Follower count verification (2K-20K range)
3. Visual content requirements check
4. Engagement and authenticity assessment`,
      useIntelligentChallengeSelection: true,
      filterMode: 'LOOSE' as const,
      pickerMode: 'OPPORTUNITY' as const,
      minViews: 0,
      minLikes: 0,
      minComments: 0,
      minFollowers: 2000,
      minRecentMedianViews: 0,
      minRecentMedianComments: 0,
      minRecentMedianLikes: 0,
      // Existing KOL filtering - exclude these unique_ids from new scouting
      excludeExistingKolIds: loadExistingKols(),
      // Image processing parameters
      uploadToOss: false,
      downloadThumbnailAsBuffer: false,
    },

    concurrentTasksLimit: 4,
    persistenceType: 'json',
    outputDirectory: './campaign-results',
    enableProgressiveReporting: true,
    reportingInterval: 1,

    enableExcelExport: true,
    enableBatchExcelExport: false,
  };

  try {
    const campaign = new ScoutCampaign(campaignConfig, mastra);
    const results = await campaign.runCampaign();

    console.log('\n🎉 Scout Campaign completed successfully!');
    console.log(`📊 Total batches: ${results.length}`);
    console.log(
      `🎯 Total unique KOLs: ${campaign.getCampaignStatus().totalUniqueKOLs}`,
    );
  } catch (error) {
    console.error('❌ Scout Campaign failed:', error);
  }
}

/**
 * Main function to choose between single workflow or campaign
 */
async function main() {
  await runScoutCampaignDemo();
}

// Run the demo
main().catch(console.error);
