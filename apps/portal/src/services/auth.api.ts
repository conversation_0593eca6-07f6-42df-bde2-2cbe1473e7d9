import { createServerFn } from '@tanstack/react-start'
import { authMiddleware } from '@/lib/middlewares/auth-middleware'

export const getUserFn = createServerFn()
  .middleware([authMiddleware])
  .handler(({ context }) => {
    return context.user
  })

export const getUserProfileFn = createServerFn().handler(() => {
  return {}
})

export const logoutFn = createServerFn().handler(async () => {})

export const invalidSessionFn = createServerFn().handler(async () => {})

export const refreshAuthTokenFn = createServerFn().handler(async () => {})
