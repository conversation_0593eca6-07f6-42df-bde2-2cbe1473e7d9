import { queryOptions } from '@tanstack/react-query'
import { getUserProfileFn, invalidSessionFn } from '@/services/auth.api'
import {
  getAuthTestFn,
  getCompanyShowcaseDataFn,
  getFAQSectionDataFn,
  getFeatureSectionDataFn,
  getGrowthSectionDataFn,
  getHeroDataFn,
  getNavbarDataFn,
  getPageQuotesDataFn,
} from '@/services/site.api'
import {
  getUserWorkflowsFn,
  getWorkflowContextsFn,
  getWorkflowSnapshotFn,
} from '@/services/portal.api'

export const authQueries = {
  all: ['auth'],
  user: () =>
    queryOptions({
      queryKey: [...authQueries.all, 'user'],
      queryFn: () => getUserProfileFn(),
    }),
  logout: () =>
    queryOptions({
      queryKey: [...authQueries.all, 'logout'],
      queryFn: () => invalidSessionFn(),
    }),
}

export const siteQueries = {
  all: ['site'],
  hero: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'hero'],
      queryFn: () => getHeroDataFn(),
    }),
  showcaseCompanies: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'showcaseCompanies'],
      queryFn: () => getCompanyShowcaseDataFn(),
    }),
  navItems: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'navbarData'],
      queryFn: () => getNavbarDataFn(),
    }),
  quotes: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'quotes'],
      queryFn: () => getPageQuotesDataFn(),
    }),
  features: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'featureSection'],
      queryFn: () => getFeatureSectionDataFn(),
    }),
  growth: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'growthSection'],
      queryFn: () => getGrowthSectionDataFn(),
    }),
  faqs: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'faqSection'],
      queryFn: () => getFAQSectionDataFn(),
    }),
  authTest: () =>
    queryOptions({
      queryKey: [...siteQueries.all, 'authTest'],
      queryFn: () => getAuthTestFn(),
    }),
}

export const portalQueries = {
  all: ['portal'],
  userWorkflows: () =>
    queryOptions({
      queryKey: [...portalQueries.all, 'userWorkflows'],
      queryFn: () => getUserWorkflowsFn(),
    }),
  workflowContexts: (workflowRunId: string) =>
    queryOptions({
      queryKey: [...portalQueries.all, 'workflowContexts', workflowRunId],
      queryFn: () => getWorkflowContextsFn({ data: { workflowRunId } }),
    }),
  workflowSnapshot: (workflowRunId: string) =>
    queryOptions({
      queryKey: [...portalQueries.all, 'workflowSnapshot', workflowRunId],
      queryFn: () => getWorkflowSnapshotFn({ data: { workflowRunId } }),
    }),
}
