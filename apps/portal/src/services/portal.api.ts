import { createServerFn } from '@tanstack/react-start'
import {
  createWorkflowAndRun,
  getUserWorkflows,
  getWorkflowSnapshot,
} from '@/lib/agent'
import { authMiddleware } from '@/lib/middlewares/auth-middleware'
import * as v from 'valibot'
import { WorkflowNames, type WorkflowWatchEvent } from '@repo/constants'
import { getWorkflowContexts } from '@/lib/agent/index'
import { authValidation } from '@/services/shared'
import {
  generateWorkflowName,
  scheduleWorkflowNameUpdate,
  portalWorkflowNameService,
} from '@/lib/agent/workflowNameService'

const createWorkflowSchema = v.object({
  workflowType: v.enum(WorkflowNames),
  triggerData: v.any(),
})

const createWorkflowAndRunSchema = v.object({
  workflowRunId: v.string(),
})

const getWorkflowSnapshotSchema = v.object({
  workflowRunId: v.string(),
})

const generateWorkflowNameSchema = v.object({
  workflowType: v.enum(WorkflowNames),
  inputParameters: v.any(),
  timestamp: v.optional(v.string()),
})

const updateWorkflowNameSchema = v.object({
  workflowRunId: v.string(),
  workflowType: v.enum(WorkflowNames),
  inputParameters: v.any(),
})

export const getUserWorkflowsFn = createServerFn()
  .middleware([authMiddleware])
  .handler(async ({ context }) => {
    const user = authValidation(context)

    const workflows = await getUserWorkflows(user.id)
    return workflows
  })

export const getWorkflowContextsFn = createServerFn()
  .middleware([authMiddleware])
  .validator(createWorkflowAndRunSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowRunId } = data
    const contexts = await getWorkflowContexts(workflowRunId)
    return contexts
  })

export const getWorkflowSnapshotFn = createServerFn()
  .middleware([authMiddleware])
  .validator(getWorkflowSnapshotSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowRunId } = data
    const snapshot = await getWorkflowSnapshot(workflowRunId)
    // const workflow = mastraClient.getWorkflow(snapshot.workflow_name)
    // console.log('registered workflow', workflow)
    // workflow.stream
    // workflow.watch({ runId: workflowRunId }, (record) => {
    //   console.log('Workflow record:', record)
    // })
    return snapshot
  })

export const createWorkflowFn = createServerFn()
  .middleware([authMiddleware])
  .validator(createWorkflowSchema)
  .handler(async ({ context, data }) => {
    const user = authValidation(context)

    const { workflowType, triggerData } = data

    // console.log('createWorkflowFn', workflowType, triggerData)

    const result = await createWorkflowAndRun(
      user.id,
      workflowType,
      triggerData,
      onRecord,
    )

    // Schedule automatic workflow name generation in the background
    if (result.runId) {
      scheduleWorkflowNameUpdate(
        result.runId,
        workflowType,
        triggerData,
        new Date(),
      )
      // console.log(`🏷️ Scheduled name generation for workflow ${result.runId}`)
    }

    console.log('------------> createWorkflowFn result', result)

    return result
  })

/**
 * Generate a workflow name using AI based on workflow type and parameters
 */
export const generateWorkflowNameFn = createServerFn()
  .middleware([authMiddleware])
  .validator(generateWorkflowNameSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowType, inputParameters, timestamp } = data

    try {
      const result = await generateWorkflowName(
        workflowType,
        inputParameters,
        timestamp ? new Date(timestamp) : undefined,
      )

      return {
        success: true,
        ...result,
      }
    } catch (error) {
      console.error('Error generating workflow name:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        workflowName: `${workflowType} - ${new Date().toLocaleString()}`,
        reasoning: 'Fallback name due to generation error',
      }
    }
  })

/**
 * Update an existing workflow's name using AI
 */
export const updateWorkflowNameFn = createServerFn()
  .middleware([authMiddleware])
  .validator(updateWorkflowNameSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowRunId, workflowType, inputParameters } = data

    try {
      await portalWorkflowNameService.updateWorkflowNameAsync(
        workflowRunId,
        workflowType,
        inputParameters,
        new Date(),
      )

      return {
        success: true,
        message: `Workflow name updated for ${workflowRunId}`,
      }
    } catch (error) {
      console.error('Error updating workflow name:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  })

/**
 * Schedule a workflow name update (fire and forget)
 */
export const scheduleWorkflowNameUpdateFn = createServerFn()
  .middleware([authMiddleware])
  .validator(updateWorkflowNameSchema)
  .handler(async ({ context, data }) => {
    authValidation(context)

    const { workflowRunId, workflowType, inputParameters } = data

    try {
      scheduleWorkflowNameUpdate(
        workflowRunId,
        workflowType,
        inputParameters,
        new Date(),
      )

      return {
        success: true,
        message: `Workflow name update scheduled for ${workflowRunId}`,
      }
    } catch (error) {
      console.error('Error scheduling workflow name update:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  })

// Serverside only
function onRecord(evt: WorkflowWatchEvent): void {
  // TODO: emit a websocket event to notify the client
  console.log('Workflow record:', evt)
}
