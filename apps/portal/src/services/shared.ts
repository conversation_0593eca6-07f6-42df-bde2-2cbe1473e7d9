export const authValidation = ({
  user,
}: {
  user: {
    id: string | undefined
    email: string | undefined
    name: string | undefined
    image: string | null | undefined
  }
}) => {
  if (!user.id) {
    console.error('No authenticated user found.')
    throw new Error('No authenticated user found.')
  }

  return {
    id: user.id!,
    email: user.email!,
    name: user.name!,
    image: user.image,
  }
}
