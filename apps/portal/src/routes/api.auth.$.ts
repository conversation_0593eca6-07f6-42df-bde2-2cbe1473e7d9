import { createAPIFileRoute } from '@tanstack/react-start/api'
import { createAuth } from '@repo/auth/server'
import { env } from '@/env/server'
import { db } from '@/db'

const auth = createAuth({
  webUrl: env.BETTER_AUTH_URL,
  authSecret: env.BETTER_AUTH_SECRET,
  db,
})

export const APIRoute = createAPIFileRoute('/api/auth/$')({
  GET: ({ request }) => {
    return auth.handler(request)
  },
  POST: ({ request }) => {
    return auth.handler(request)
  },
})
