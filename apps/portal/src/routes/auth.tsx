import { createFileRoute } from '@tanstack/react-router'
import { useSuspenseQuery } from '@tanstack/react-query'
import { siteQueries } from '@/services/queries'

export const Route = createFileRoute('/auth')({
  component: RouteComponent,
  loader: async ({ context }) => {
    const queryClient = context.queryClient
    const [authTest] = await Promise.all([
      queryClient.prefetchQuery(siteQueries.authTest()),
    ])
    return {
      authTest,
    }
  },
})

function RouteComponent() {
  const { data: authTest } = useSuspenseQuery(siteQueries.authTest())
  return <div> {authTest && authTest.content}</div>
}
