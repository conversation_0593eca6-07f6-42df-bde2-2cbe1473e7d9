import { useState, useMemo } from 'react'
import { portalQueries } from '@/services/queries'
import { createFileRoute, Link, useNavigate } from '@tanstack/react-router'
import {
  ArrowLeft,
  ClipboardCopy,
  FileText,
  MessageSquare,
  RefreshCw,
} from 'lucide-react'
import { WorkflowNames } from '@repo/constants'
import { StatusBadge } from '@/components/status-badge'

import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'
import { Icons } from '@/components/icons'

export const Route = createFileRoute('/_dashboard/workflows/$wid')({
  component: RouteComponent,
  loader: async ({ context, params }) => {
    const { queryClient } = context
    const { wid } = params
    try {
      const snapshot = await queryClient.fetchQuery(
        portalQueries.workflowSnapshot(wid),
      )
      const contexts = await queryClient.fetchQuery(
        portalQueries.workflowContexts(wid),
      )
      return { snapshot, contexts, wid, isInitializing: false }
    } catch (error) {
      console.warn('Workflow might not be created, show loading')
      return { snapshot: null, contexts: null, wid, isInitializing: true }
    }

    // console.log('snapshot', JSON.stringify(snapshot, null, 2))
  },
})

// Helper function to format dates
function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  })
}

// Helper function to get workflow icon
function getWorkflowIcon(workflowName: string): React.ReactNode {
  switch (workflowName) {
    case WorkflowNames.aiScriptWorkflow:
      return <FileText className="h-5 w-5" />
    default:
      return <MessageSquare className="h-5 w-5" />
  }
}

// Helper function to get step dialog message
function getStepDialogMessage(stepId: string): string {
  const stepMessages: Record<string, string> = {
    'analyze-description':
      'Analyzing your campaign description to identify key themes and target audience.',
    'find-best-challenges':
      'Searching for the most relevant TikTok challenges that match your campaign.',
    'collect-good-videos':
      'Collecting high-performing videos from the identified challenges for analysis.',
    'generate-script':
      'Generating creative scripts based on successful video patterns.',
    'optimize-content':
      'Optimizing content recommendations for maximum engagement.',
    'finalize-strategy':
      'Finalizing your comprehensive TikTok marketing strategy.',
  }

  return stepMessages[stepId] || 'Processing this step of your workflow.'
}

function RouteComponent() {
  const { snapshot, contexts, wid, isInitializing } = Route.useLoaderData()
  const [activeStep, setActiveStep] = useState<number>(0)
  const navigate = useNavigate()

  // Function to refresh the workflow data
  const refreshWorkflow = () => {
    navigate({
      to: '/workflows/$wid',
      params: { wid: snapshot?.run_id || wid },
      replace: true,
    })
  }

  // If workflow is still initializing, show loading state
  if (isInitializing) {
    return (
      <div className="container mx-auto py-6 max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 border-b pb-4">
          <div className="flex items-center gap-2">
            <Link
              to="/portal"
              className="mr-2 p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <div className="flex items-center gap-3">
              <div className="bg-gray-100 p-2 rounded-full">
                <MessageSquare className="h-5 w-5" />
              </div>
              <div>
                <h1 className="text-xl font-medium">Initializing Workflow</h1>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>Run ID: {wid}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5"
                    onClick={() => {
                      navigator.clipboard.writeText(wid)
                      toast.success('Run ID copied to clipboard!')
                    }}
                  >
                    <ClipboardCopy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="pending" />
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={refreshWorkflow}
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Initialization Content */}
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <div className="bg-gray-100 p-4 rounded-full mb-6">
            <RefreshCw className="h-8 w-8 animate-spin text-gray-600" />
          </div>
          <h2 className="text-2xl font-semibold mb-4">Workflow Initializing</h2>
          <p className="text-muted-foreground mb-6 max-w-md">
            Your workflow is being set up and will begin processing shortly.
            This usually takes a few moments.
          </p>
          <div className="space-y-3 text-left">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm">Creating workflow instance...</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span className="text-sm text-muted-foreground">
                Preparing first step...
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span className="text-sm text-muted-foreground">
                Starting execution...
              </span>
            </div>
          </div>
          <Button
            variant="outline"
            className="mt-8 gap-2"
            onClick={refreshWorkflow}
          >
            <RefreshCw className="h-4 w-4" />
            Check Status
          </Button>
        </div>
      </div>
    )
  }

  // Ensure snapshot exists before proceeding
  if (!snapshot) {
    return (
      <div className="container mx-auto py-6 max-w-6xl">
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <h2 className="text-2xl font-semibold mb-4">Workflow Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The requested workflow could not be found.
          </p>
          <Link to="/portal">
            <Button>Return to Portal</Button>
          </Link>
        </div>
      </div>
    )
  }

  // Extract workflow steps from the snapshot
  const workflowSteps = useMemo(() => {
    if (!snapshot?.snapshot?.context) return []

    // Filter out 'input' as it's not a step, and only include entries with status
    return Object.entries(snapshot.snapshot.context)
      .filter(
        ([stepId, stepData]) =>
          stepId !== 'input' &&
          stepData &&
          typeof stepData === 'object' &&
          'status' in stepData,
      )
      .map(([stepId, stepData], index) => {
        // Type guard for stepData
        const step = stepData as {
          status: string
          output?: any
          error?: string
        }

        return {
          id: index + 1,
          stepId,
          status: step.status,
          date: formatDate(snapshot.snapshot.timestamp),
          title: stepId
            .split('-')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' '),
          description:
            step.status === 'success' && step.output
              ? JSON.stringify(step.output).substring(0, 100) + '...'
              : step.status === 'failed' && step.error
                ? step.error
                : 'No description available',
          payload: step.status === 'success' ? step.output : undefined,
          error: step.status === 'failed' ? step.error : undefined,
        }
      })
      .sort((a, b) => a.id - b.id)
  }, [snapshot])

  // Get the current active step based on status
  const currentActiveStep = useMemo(() => {
    const completedSteps = workflowSteps.filter(
      (step) => step.status === 'success',
    ).length

    return completedSteps
  }, [workflowSteps])

  // Process contexts data (keeping for future use)
  useMemo(() => {
    if (!contexts || !Array.isArray(contexts)) return []

    return contexts.map((context) => ({
      id: context.id,
      type: context.contextType,
      data: context.contextData,
      createdAt: new Date(context.createdAt).toLocaleString(),
    }))
  }, [contexts])

  // Get workflow metadata
  const workflowMeta = useMemo(() => {
    // console.log('snapshot', JSON.stringify(snapshot))
    if (!snapshot) {
      return {
        name: 'Unknown Workflow',
        runId: wid,
        status: 'pending',
        startedAt: 'N/A',
        input: {},
      }
    }

    return {
      name: snapshot.workflow_name || 'Unknown Workflow',
      runId: snapshot.run_id,
      status:
        snapshot.snapshot.activePaths.length > 0
          ? snapshot.snapshot.activePaths[
              snapshot.snapshot.activePaths.length - 1
            ].status
          : 'success',
      startedAt: formatDate(snapshot.snapshot.timestamp),
      input: snapshot.snapshot.context.input || {},
    }
  }, [snapshot, workflowSteps, wid])

  function processNameStringFormat(key: string) {
    // Capitalize the first letter + add spaces between words
    return key
      .split(/(?=[A-Z])/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  return (
    <div className="container mx-auto py-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 border-b pb-4">
        <div className="flex items-center gap-2">
          <Link
            to="/portal"
            className="mr-2 p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div className="flex items-center gap-3">
            <div className="bg-gray-100 p-2 rounded-full">
              {getWorkflowIcon(workflowMeta.name)}
            </div>
            <div>
              <h1 className="text-xl font-medium">
                {processNameStringFormat(workflowMeta.name)}
              </h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Run ID: {workflowMeta.runId}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5"
                  onClick={() => {
                    navigator.clipboard.writeText(workflowMeta.runId)
                    toast.success('Run ID copied to clipboard!')
                  }}
                >
                  <ClipboardCopy className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <StatusBadge status={workflowMeta.status} />
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={refreshWorkflow}
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Left sidebar - Workflow Details and Timeline */}
        <div className="md:col-span-1">
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Workflow Details</CardTitle>
              <CardDescription className="text-xs">
                Information about this workflow run
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-0">
              <div>
                <h3 className="text-sm font-medium mb-1">Started</h3>
                <p className="text-sm text-muted-foreground">
                  {workflowMeta.startedAt}
                </p>
              </div>

              <div>
                {Object.entries(workflowMeta.input).map(([key, value]) => (
                  <div key={key}>
                    <h3 className="text-sm font-medium mb-1">
                      {processNameStringFormat(key)}
                    </h3>
                    <p className="text-sm text-muted-foreground">{value}</p>
                  </div>
                ))}
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Progress</h3>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full"
                    style={{
                      width: `${(currentActiveStep / workflowSteps.length) * 100}%`,
                    }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {currentActiveStep} of {workflowSteps.length} steps completed
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Workflow Timeline</CardTitle>
              <CardDescription className="text-xs">
                Step by step progress of your workflow
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="pl-4 relative border-l border-gray-200">
                {workflowSteps.map((step) => (
                  <div
                    key={step.id}
                    className={cn(
                      'mb-6 cursor-pointer',
                      activeStep === step.id - 1
                        ? 'opacity-100'
                        : 'opacity-70 hover:opacity-100',
                    )}
                    onClick={() => setActiveStep(step.id - 1)}
                  >
                    <div className="absolute -left-1.5">
                      <div
                        className={cn(
                          'w-3 h-3 rounded-full border-2',
                          step.status === 'success'
                            ? 'bg-green-500 border-green-500'
                            : 'bg-white border-gray-300',
                        )}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 mb-1">
                      {step.date}
                    </div>
                    <div className="font-medium text-sm mb-1">{step.title}</div>
                    <StatusBadge status={step.status} size="sm" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main content area - Step Details */}
        <div className="md:col-span-3">
          {workflowSteps.length > 0 && activeStep < workflowSteps.length && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">
                      {workflowSteps[activeStep]?.title || 'Step Details'}
                    </CardTitle>
                    <CardDescription className="text-xs">
                      Detailed information for the selected step
                    </CardDescription>
                  </div>
                  <StatusBadge
                    status={workflowSteps[activeStep]?.status || 'pending'}
                  />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {/* Agent Chat Bubbles Placeholder */}
                <div className="mb-6 space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <Icons.logo className="h-5 w-5" />
                    </div>
                    <div className="bg-gray-100 rounded-lg p-4 max-w-[80%]">
                      <p className="text-sm">
                        {getStepDialogMessage(
                          workflowSteps[activeStep]?.stepId || '',
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Step Output */}
                <div className="bg-gray-50 p-4 rounded-md overflow-auto max-h-80 border border-gray-100">
                  <pre className="text-sm">
                    {workflowSteps[activeStep]?.status === 'success' &&
                    workflowSteps[activeStep]?.payload
                      ? JSON.stringify(
                          workflowSteps[activeStep].payload,
                          null,
                          2,
                        )
                      : workflowSteps[activeStep]?.status === 'failed' &&
                          workflowSteps[activeStep]?.error
                        ? workflowSteps[activeStep].error
                        : 'No output available'}
                  </pre>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between pt-4 border-t mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
                  disabled={activeStep === 0}
                >
                  Previous Step
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setActiveStep(
                      Math.min(workflowSteps.length - 1, activeStep + 1),
                    )
                  }
                  disabled={activeStep === workflowSteps.length - 1}
                >
                  Next Step
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
