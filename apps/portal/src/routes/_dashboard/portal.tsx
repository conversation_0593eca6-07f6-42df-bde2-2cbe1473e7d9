import { createFileRoute } from '@tanstack/react-router'
import { Suspense } from 'react'
import { ErrorBoundary } from '@/components/error-boundary'
import { portalQueries } from '@/services/queries'
import { CreateWorkflowDialog } from '@/components/workflows/create-workflow-dialog'
import { WorkflowCard } from '@/components/workflows/workflow-card'
import { WorkflowsEmptyState } from '@/components/workflows/empty-state'
import { WorkflowError } from '@/components/workflows/workflow-error'
import { WorkflowGridSkeleton } from '@/components/workflows/workflow-skeleton'
import { useWorkflows } from '@/hooks/use-workflows'

export const Route = createFileRoute('/_dashboard/portal')({
  component: PortalPage,
  loader: async ({ context }) => {
    const queryClient = context.queryClient
    await queryClient.prefetchQuery(portalQueries.userWorkflows())
    return {}
  },
})

function PortalContent() {
  const { workflows, isError, error, refetch, isEmpty, count } = useWorkflows()

  if (isError) {
    return <WorkflowError error={error} onRetry={() => refetch()} />
  }

  if (isEmpty) {
    return <WorkflowsEmptyState />
  }

  return (
    <>
      <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
        {count} workflow{count !== 1 ? 's' : ''} • Sorted by newest
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {workflows.map((workflow) => (
          <WorkflowCard key={workflow.id} workflow={workflow} />
        ))}
      </div>
    </>
  )
}

function PortalPage() {
  return (
    <div className="mx-auto px-4 py-8">
      {/* Page Header */}
      <header className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            My Workflows
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage and monitor your automation workflows
          </p>
        </div>
        <CreateWorkflowDialog />
      </header>

      {/* Main Content */}
      <main>
        <ErrorBoundary
          fallback={({ error, reset }) => (
            <WorkflowError error={error} onRetry={reset} />
          )}
        >
          <Suspense fallback={<WorkflowGridSkeleton />}>
            <PortalContent />
          </Suspense>
        </ErrorBoundary>
      </main>
    </div>
  )
}
