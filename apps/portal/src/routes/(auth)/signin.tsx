import { useState } from 'react'
import { Link, createFileRoute } from '@tanstack/react-router'
import { FcGoogle } from 'react-icons/fc'
import { FaGithub } from 'react-icons/fa'
import { Loader } from 'lucide-react'
import type { FormEvent } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { signIn } from '@/lib/auth-client'

export const Route = createFileRoute('/(auth)/signin')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <main className="flex flex-col items-center justify-center h-screen">
      <LoginForm />
    </main>
  )
}

function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isEmailLoading, setIsEmailLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [isGithubLoading, setIsGithubLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  async function handleGoogleLogin() {
    try {
      setIsGoogleLoading(true)
      setError(null)
      await signIn.social({
        provider: 'google',
      })
    } catch (err) {
      console.error('Google login error:', err)
      setError('Failed to login with Google. Please try again.')
      setIsGoogleLoading(false)
    }
  }

  async function handleGithubLogin() {
    try {
      setIsGithubLoading(true)
      setError(null)
      await signIn.social({
        provider: 'github',
      })
    } catch (err) {
      console.error('Github login error:', err)
      setError('Failed to login with Github. Please try again.')
      setIsGithubLoading(false)
    }
  }

  async function handleEmailLogin(e: FormEvent) {
    e.preventDefault()

    if (!email || !password) {
      setError('Email and password are required')
      return
    }

    try {
      setIsEmailLoading(true)
      setError(null)
      const resp = await signIn.email({
        email,
        password,
      })
      if (resp.error) {
        console.error('Email login error:', resp.error)
        setError('Invalid email or password. Please try again.')
      } else {
        console.log('email login resp', resp.data)
      }
    } catch (err) {
      console.error('Email login error:', err)
      setError('Invalid email or password. Please try again.')
    } finally {
      setIsEmailLoading(false)
    }
  }

  return (
    <Card className="max-w-sm w-full px-4">
      <CardHeader>
        <CardTitle className="text-2xl">Login</CardTitle>
        <CardDescription>
          Enter your email below to login to your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-2 text-sm text-red-500 bg-red-50 dark:bg-red-950/30 rounded-md">
            {error}
          </div>
        )}
        <form onSubmit={handleEmailLogin} className="grid gap-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleGoogleLogin}
            disabled={isGoogleLoading || isGithubLoading || isEmailLoading}
            type="button"
          >
            {isGoogleLoading ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FcGoogle className="w-4 h-4 mr-2" />
            )}
            <span className="text-primary">Continue with Google</span>
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={handleGithubLogin}
            disabled={isGoogleLoading || isGithubLoading || isEmailLoading}
            type="button"
          >
            {isGithubLoading ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FaGithub className="w-4 h-4 mr-2 text-primary" />
            )}
            <span className="text-primary">Continue with Github</span>
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isEmailLoading || isGoogleLoading || isGithubLoading}
            />
          </div>
          <div className="grid gap-2">
            <div className="flex items-center">
              <Label htmlFor="password">Password</Label>
            </div>
            <Input
              id="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isEmailLoading || isGoogleLoading || isGithubLoading}
            />
          </div>
          <Button
            variant="secondary"
            type="submit"
            className="w-full"
            disabled={isEmailLoading || isGoogleLoading || isGithubLoading}
          >
            {isEmailLoading ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            <div className="text-white">Login</div>
          </Button>
        </form>
        <div className="mt-4 text-center text-sm">
          Don&apos;t have an account?{' '}
          <Link className="underline" to={'/signup'}>
            Sign up
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
