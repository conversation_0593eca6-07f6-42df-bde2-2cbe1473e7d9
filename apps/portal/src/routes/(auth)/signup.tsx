import { useState } from 'react'
import { Link, createFileRoute, useNavigate } from '@tanstack/react-router'
import { FcGoogle } from 'react-icons/fc'
import { FaGithub } from 'react-icons/fa'
import { Loader } from 'lucide-react'
import type { FormEvent } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { signIn, signUp } from '@/lib/auth-client'

export const Route = createFileRoute('/(auth)/signup')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <main className="flex flex-col items-center justify-center h-screen">
      <SignupForm />
    </main>
  )
}

function SignupForm() {
  const navigate = useNavigate()
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isSignupLoading, setIsSignupLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [isGithubLoading, setIsGithubLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  async function handleGoogleSignup() {
    try {
      setIsGoogleLoading(true)
      setError(null)
      await signIn.social({
        provider: 'google',
      })
    } catch (err) {
      console.error('Google signup error:', err)
      setError('Failed to sign up with Google. Please try again.')
      setIsGoogleLoading(false)
    }
  }

  async function handleGithubSignup() {
    try {
      setIsGithubLoading(true)
      setError(null)
      await signIn.social({
        provider: 'github',
      })
    } catch (err) {
      console.error('Github signup error:', err)
      setError('Failed to sign up with Github. Please try again.')
      setIsGithubLoading(false)
    }
  }

  async function handleEmailSignup(e: FormEvent) {
    e.preventDefault()

    if (!firstName || !lastName || !email || !password) {
      setError('All fields are required')
      return
    }

    try {
      setIsSignupLoading(true)
      setError(null)

      const resp = await signUp.email({
        name: `${firstName} ${lastName}`,
        email,
        password,
      })

      console.log('email signup resp', resp)

      if (resp.error) {
        setError(
          resp.error.message || 'Failed to create account. Please try again.',
        )
      } else {
        // Redirect to signin page or dashboard after successful signup
        navigate({ to: '/signin' })
      }
    } catch (err: any) {
      console.error('Email signup error:', err)
      setError(err.message || 'Failed to create account. Please try again.')
    } finally {
      setIsSignupLoading(false)
    }
  }

  const isLoading = isSignupLoading || isGoogleLoading || isGithubLoading

  return (
    <Card className="mx-auto max-w-sm">
      <CardHeader>
        <CardTitle className="text-xl">Sign Up</CardTitle>
        <CardDescription>
          Enter your information to create an account
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-2 text-sm text-red-500 bg-red-50 dark:bg-red-950/30 rounded-md">
            {error}
          </div>
        )}
        <form onSubmit={handleEmailSignup} className="grid gap-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleGoogleSignup}
            disabled={isLoading}
            type="button"
          >
            {isGoogleLoading ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FcGoogle className="w-4 h-4 mr-2" />
            )}
            <span className="text-primary">Continue with Google</span>
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={handleGithubSignup}
            disabled={isLoading}
            type="button"
          >
            {isGithubLoading ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FaGithub className="w-4 h-4 mr-2 text-primary" />
            )}
            <span className="text-primary">Continue with Github</span>
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="first-name">First name</Label>
              <Input
                id="first-name"
                placeholder="Max"
                required
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="last-name">Last name</Label>
              <Input
                id="last-name"
                placeholder="Robinson"
                required
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                disabled={isLoading}
              />
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <Button
            variant="secondary"
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isSignupLoading ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            <div className="text-white">Create an account</div>
          </Button>
        </form>
        <div className="mt-4 text-center text-sm">
          Already have an account?{' '}
          <Link className="underline" to={'/signin'}>
            Sign in
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
