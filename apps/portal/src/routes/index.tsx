import { createFileRoute } from '@tanstack/react-router'
import { useSuspenseQueries } from '@tanstack/react-query'
import { HeroSection } from '@/components/sections/hero-section'
import { siteQueries } from '@/services/queries'
import { CompanyShowcase } from '@/components/sections/company-showcase'
import { QuoteSection } from '@/components/sections/quote-section'
import { FeatureSection } from '@/components/sections/feature-section'
import { FooterSection } from '@/components/sections/footer-section'
import { GrowthSection } from '@/components/sections/growth-section'
import { FAQSection } from '@/components/sections/faq-section'
import { Navbar } from '@/components/sections/navbar'
import { getUserFn } from '@/services/auth.api'

export const Route = createFileRoute('/')({
  component: App,
  loader: async ({ context }) => {
    const user = await getUserFn()
    const queryClient = context.queryClient
    const [
      navData,
      hero,
      companyShowcase,
      quotes,
      featureSection,
      growth,
      faqSection,
    ] = await Promise.all([
      queryClient.prefetchQuery(siteQueries.navItems()),
      queryClient.prefetchQuery(siteQueries.hero()),
      queryClient.prefetchQuery(siteQueries.showcaseCompanies()),
      queryClient.prefetchQuery(siteQueries.quotes()),
      queryClient.prefetchQuery(siteQueries.features()),
      queryClient.prefetchQuery(siteQueries.growth()),
      queryClient.prefetchQuery(siteQueries.faqs()),
    ])
    return {
      navData,
      hero,
      companyShowcase,
      quotes,
      featureSection,
      growth,
      faqSection,
      user,
    }
  },
})

function App() {
  const {
    navData,
    hero,
    companyShowcase,
    quotes,
    featureSection,
    growth,
    faqSection,
  } = useSuspenseQueries({
    queries: [
      siteQueries.navItems(),
      siteQueries.hero(),
      siteQueries.showcaseCompanies(),
      siteQueries.quotes(),
      siteQueries.features(),
      siteQueries.growth(),
      siteQueries.faqs(),
    ],
    combine: (results) => {
      return {
        navData: results[0].data,
        hero: results[1].data,
        companyShowcase: results[2].data,
        quotes: results[3].data,
        featureSection: results[4].data,
        growth: results[5].data,
        faqSection: results[6].data,
      }
    },
  })

  const { user } = Route.useLoaderData()

  return (
    <>
      <Navbar data={navData} user={user} />
      <main className="flex flex-col items-center justify-center divide-y divide-border min-h-screen w-full">
        <HeroSection data={hero} user={user} />
        <CompanyShowcase data={companyShowcase} />
        <FeatureSection data={featureSection} />
        {quotes.quotes.length > 0 &&
          quotes.quotes.map((quote, idx) => (
            <QuoteSection data={quote} key={idx} />
          ))}
        <GrowthSection data={growth} />
        <FAQSection data={faqSection} />
        <FooterSection
          appName={'Jelly Otter'}
          heroDescription={hero.description}
          slogan={'Streamline your workflow'}
        />
      </main>
    </>
  )
}
