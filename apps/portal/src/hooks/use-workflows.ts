import { useSuspenseQuery } from '@tanstack/react-query'
import { portalQueries } from '@/services/queries'
import type { UserWorkflow, WorkflowUI } from '@/types/workflow'
import { getWorkflowIcon } from '@/types/workflow'
import { normalizeWorkflowStatus } from '@/utils/workflow-utils'

export function useWorkflows() {
  const {
    data: userWorkflows,
    isError,
    error,
    refetch,
  } = useSuspenseQuery(portalQueries.userWorkflows())

  // Transform the workflow data to UI format with normalized status
  const workflows: WorkflowUI[] = Array.isArray(userWorkflows)
    ? userWorkflows
        .map((workflow: UserWorkflow) => ({
          id: workflow.workflowRunId,
          title: workflow.workflowName,
          icon: getWorkflowIcon(workflow.workflowName),
          status: normalizeWorkflowStatus(workflow.status),
          createdAt: workflow.createdAt,
          updatedAt: workflow.updatedAt,
        }))
        // Sort by creation date, newest first
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        )
    : []

  return {
    workflows,
    isError,
    error,
    refetch,
    isEmpty: workflows.length === 0,
    count: workflows.length,
  }
}
