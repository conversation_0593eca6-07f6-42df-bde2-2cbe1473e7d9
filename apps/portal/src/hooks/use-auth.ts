import { useQuery } from '@tanstack/react-query'
import { getUserFn } from '@/services/auth.api'

export interface User {
  id: string
  email?: string
  name?: string
  image?: string
}

export function useAuth() {
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['user'],
    queryFn: () => getUserFn(),
  })

  return {
    user,
    isLoading,
    error,
    isAuthenticated: !!user?.id,
  }
}
