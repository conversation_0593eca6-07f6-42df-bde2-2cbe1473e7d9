import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import * as v from 'valibot'
import { useAuth } from '@/hooks/use-auth'
import { portalQueries } from '@/services/queries'
import { createWorkflowFn } from '@/services/portal.api'
import { WorkflowNames } from '@repo/constants'
import type { WorkflowFormData } from '@/types/workflow-form'
import {
  workflowFormSchema,
  getInitialFormData,
  formatValidationErrors,
} from '@/types/workflow-form'

interface UseCreateWorkflowOptions {
  onSuccess?: (runId: string) => void
}

export function useCreateWorkflow(options?: UseCreateWorkflowOptions) {
  const [formData, setFormData] =
    useState<WorkflowFormData>(getInitialFormData())
  const [errors, setErrors] = useState<Record<string, string>>({})
  const { user } = useAuth()
  const navigate = useNavigate()
  const queryClient = useQueryClient()

  // Mutation for creating a workflow
  const mutation = useMutation({
    mutationFn: async (data: WorkflowFormData) => {
      if (!user?.id) {
        throw new Error('User not authenticated')
      }

      // Prepare trigger data based on workflow type
      const triggerData =
        data.workflowType === WorkflowNames.creatorScoutWorkflow
          ? { targetCreatorDescription: data.campaignDescription }
          : { campaignDescription: data.campaignDescription }

      // Create and run the workflow
      const { runId } = await createWorkflowFn({
        data: {
          workflowType: data.workflowType,
          triggerData,
        },
      })

      return { runId }
    },
    onSuccess: (data) => {
      // Invalidate the user workflows query to refresh the list
      queryClient.invalidateQueries({ queryKey: portalQueries.all })

      // Reset form
      setFormData(getInitialFormData())
      setErrors({})

      if (options?.onSuccess) {
        options.onSuccess(data.runId)
      } else {
        // Navigate to the workflow details page
        navigate({ to: '/workflows/$wid', params: { wid: data.runId } })
      }
    },
    onError: (error) => {
      // Handle API errors
      if (error instanceof Error) {
        setErrors({ submit: error.message })
      }
    },
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form data
    try {
      v.parse(workflowFormSchema, formData)
      setErrors({})

      // Submit the form
      mutation.mutate(formData)
    } catch (error) {
      if (error instanceof v.ValiError) {
        setErrors(formatValidationErrors(error))
      }
    }
  }

  const updateField = (field: keyof WorkflowFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const resetForm = () => {
    setFormData(getInitialFormData())
    setErrors({})
  }

  return {
    formData,
    errors,
    isSubmitting: mutation.isPending,
    handleSubmit,
    updateField,
    resetForm,
  }
}
