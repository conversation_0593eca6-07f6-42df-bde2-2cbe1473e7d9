// Workflow types
export interface UserWorkflow {
  id: number
  userId: string
  workflowRunId: string
  workflowName: string
  status: string
  createdAt: Date
  updatedAt: Date
}

export interface WorkflowUI {
  id: string
  title: string
  icon: string
  status: string
  createdAt: Date
  updatedAt: Date
}

export type WorkflowStatus = 'completed' | 'running' | 'failed' | 'pending'

export const WORKFLOW_ICONS: Record<string, string> = {
  aiScriptWorkflow: '📝',
  creatorScoutWorkflow: '🕵️',
  default: '🔄',
} as const

export const getWorkflowIcon = (workflowName: string): string => {
  return WORKFLOW_ICONS[workflowName] || WORKFLOW_ICONS.default
}
