import * as v from 'valibot'
import { WorkflowNames } from '@repo/constants'

// Workflow type configurations
export const WORKFLOW_CONFIGS = {
  [WorkflowNames.aiScriptWorkflow]: {
    label: 'AI Script Generator',
    icon: '📝',
    description: 'Generate engaging scripts for your content using AI',
    placeholder:
      'Describe your campaign in detail. For example: Create a TikTok campaign for a mobile game targeting Gen Z users who enjoy casual gaming.',
    fieldLabel: 'Campaign Description',
  },
  [WorkflowNames.creatorScoutWorkflow]: {
    label: 'Creator Scout',
    icon: '🕵️',
    description: 'Find the perfect creators for your campaign',
    placeholder:
      'Describe your ideal creator profile. For example: Looking for fitness influencers with 10k-100k followers who create workout content.',
    fieldLabel: 'Target Creator Description',
  },
} as const

// Schema for workflow creation form
export const workflowFormSchema = v.object({
  workflowType: v.enum(WorkflowNames),
  campaignDescription: v.pipe(
    v.string(),
    v.minLength(
      10,
      'Please provide a detailed description (at least 10 characters)',
    ),
    v.maxLength(1000, 'Description is too long (max 1000 characters)'),
  ),
})

export type WorkflowFormData = v.InferInput<typeof workflowFormSchema>

// Initial form state
export const getInitialFormData = (): WorkflowFormData => ({
  workflowType: WorkflowNames.aiScriptWorkflow,
  campaignDescription: '',
})

// Error formatting
export function formatValidationErrors(
  error: v.ValiError<typeof workflowFormSchema>,
): Record<string, string> {
  const errors: Record<string, string> = {}
  error.issues.forEach((issue: v.BaseIssue<unknown>) => {
    if (issue.path && issue.path.length > 0) {
      const key = issue.path[0]?.key
      if (key) {
        errors[String(key)] = issue.message
      }
    }
  })
  return errors
}
