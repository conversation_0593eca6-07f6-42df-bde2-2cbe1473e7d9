import {
  formatDistanceToNow,
  format,
  isToday,
  isYesterday,
  isThisWeek,
  isThisYear,
} from 'date-fns'

/**
 * Format a date to a human-readable relative time
 * Examples: "2 hours ago", "yesterday", "3 days ago"
 */
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  return formatDistanceToNow(dateObj, { addSuffix: true })
}

/**
 * Format a date with smart formatting based on how recent it is
 * - Today: "Today at 3:45 PM"
 * - Yesterday: "Yesterday at 3:45 PM"
 * - This week: "Monday at 3:45 PM"
 * - This year: "Mar 15 at 3:45 PM"
 * - Older: "Mar 15, 2023"
 */
export function formatSmartDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  if (isToday(dateObj)) {
    return `Today at ${format(dateObj, 'h:mm a')}`
  }

  if (isYesterday(dateObj)) {
    return `Yesterday at ${format(dateObj, 'h:mm a')}`
  }

  if (isThisWeek(dateObj)) {
    return format(dateObj, "EEEE 'at' h:mm a")
  }

  if (isThisYear(dateObj)) {
    return format(dateObj, "MMM d 'at' h:mm a")
  }

  return format(dateObj, 'MMM d, yyyy')
}

/**
 * Format a date for display in a compact format
 * Examples: "2h ago", "3d ago", "Mar 15"
 */
export function formatCompactDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInMs = now.getTime() - dateObj.getTime()
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
    return `${diffInMinutes}m ago`
  }

  if (diffInHours < 24) {
    return `${diffInHours}h ago`
  }

  if (diffInDays < 7) {
    return `${diffInDays}d ago`
  }

  if (isThisYear(dateObj)) {
    return format(dateObj, 'MMM d')
  }

  return format(dateObj, 'MMM d, yyyy')
}
