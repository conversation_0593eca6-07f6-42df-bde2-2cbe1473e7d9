import type { WorkflowStatus } from '@/types/workflow'

/**
 * Maps workflow status strings to normalized status types
 */
export function normalizeWorkflowStatus(status: string): WorkflowStatus {
  const statusMap: Record<string, WorkflowStatus> = {
    completed: 'completed',
    success: 'completed',
    running: 'running',
    in_progress: 'running',
    failed: 'failed',
    error: 'failed',
    pending: 'pending',
    queued: 'pending',
  }

  return (statusMap[status.toLowerCase()] || 'pending') as WorkflowStatus
}

/**
 * Gets a human-readable label for a workflow status
 */
export function getWorkflowStatusLabel(status: WorkflowStatus): string {
  const labels: Record<WorkflowStatus, string> = {
    completed: 'Completed',
    running: 'Running',
    failed: 'Failed',
    pending: 'Pending',
  }

  return labels[status] || 'Unknown'
}

/**
 * Gets the color scheme for a workflow status
 */
export function getWorkflowStatusColor(status: WorkflowStatus): {
  bg: string
  text: string
  border: string
} {
  const colors = {
    completed: {
      bg: 'bg-green-50 dark:bg-green-950/20',
      text: 'text-green-700 dark:text-green-400',
      border: 'border-green-200 dark:border-green-900',
    },
    running: {
      bg: 'bg-blue-50 dark:bg-blue-950/20',
      text: 'text-blue-700 dark:text-blue-400',
      border: 'border-blue-200 dark:border-blue-900',
    },
    failed: {
      bg: 'bg-red-50 dark:bg-red-950/20',
      text: 'text-red-700 dark:text-red-400',
      border: 'border-red-200 dark:border-red-900',
    },
    pending: {
      bg: 'bg-gray-50 dark:bg-gray-950/20',
      text: 'text-gray-700 dark:text-gray-400',
      border: 'border-gray-200 dark:border-gray-900',
    },
  }

  return colors[status] || colors.pending
}
