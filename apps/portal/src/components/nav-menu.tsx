'use client'

import { motion } from 'motion/react'
import React, { useRef, useState } from 'react'
import type { NavItemData } from '@/services/site.api'

export interface NavMenuProps {
  data: Array<NavItemData>
}

export function NavMenu({ ...props }: NavMenuProps) {
  const { data } = props
  const navs = data

  const ref = useRef<HTMLUListElement>(null)
  const [left, setLeft] = useState(0)
  const [width, setWidth] = useState(0)
  const [isReady, setIsReady] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')
  const [isManualScroll, setIsManualScroll] = useState(false)

  React.useEffect(() => {
    // Initialize with first nav item
    const firstItem = ref.current?.querySelector(
      `[href="#${navs[0].url.substring(1)}"]`,
    )?.parentElement
    if (firstItem) {
      const rect = firstItem.getBoundingClientRect()
      setLeft(firstItem.offsetLeft)
      setWidth(rect.width)
      setIsReady(true)
    }
  }, [])

  React.useEffect(() => {
    const handleScroll = () => {
      // Skip scroll handling during manual click scrolling
      if (isManualScroll) return

      const sections = navs.map((item) => item.url.substring(1))

      // Find the section closest to viewport top
      let closestSection = sections[0]
      let minDistance = Infinity

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const rect = element.getBoundingClientRect()
          const distance = Math.abs(rect.top - 100) // Offset by 100px to trigger earlier
          if (distance < minDistance) {
            minDistance = distance
            closestSection = section
          }
        }
      }

      // Update active section and nav indicator
      setActiveSection(closestSection)
      const navItem = ref.current?.querySelector(
        `[href="#${closestSection}"]`,
      )?.parentElement
      if (navItem) {
        const rect = navItem.getBoundingClientRect()
        setLeft(navItem.offsetLeft)
        setWidth(rect.width)
      }
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Initial check
    return () => window.removeEventListener('scroll', handleScroll)
  }, [isManualScroll])

  const handleClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    item: NavItemData,
  ) => {
    e.preventDefault()

    const targetId = item.url.substring(1)
    const element = document.getElementById(targetId)

    if (element) {
      // Set manual scroll flag
      setIsManualScroll(true)

      // Immediately update nav state
      setActiveSection(targetId)
      const navItem = e.currentTarget.parentElement
      if (navItem) {
        const rect = navItem.getBoundingClientRect()
        setLeft(navItem.offsetLeft)
        setWidth(rect.width)
      }

      // Calculate exact scroll position
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - 100 // 100px offset

      // Smooth scroll to exact position
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      })

      // Reset manual scroll flag after animation completes
      setTimeout(() => {
        setIsManualScroll(false)
      }, 500) // Adjust timing to match scroll animation duration
    }
  }

  return (
    <div className="w-full hidden md:block">
      <ul
        className="relative mx-auto flex w-fit rounded-full h-11 px-2 items-center justify-center"
        ref={ref}
      >
        {navs.map((item) => (
          <li
            key={item.name}
            className={`z-10 cursor-pointer h-full flex items-center justify-center px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeSection === item.url.substring(1)
                ? 'text-primary'
                : 'text-primary/60 hover:text-primary'
            } tracking-tight`}
          >
            <a href={item.url} onClick={(e) => handleClick(e, item)}>
              {item.name}
            </a>
          </li>
        ))}
        {isReady && (
          <motion.li
            animate={{ left, width }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
            className="absolute inset-0 my-1.5 rounded-full bg-accent/60 border border-border"
          />
        )}
      </ul>
    </div>
  )
}
