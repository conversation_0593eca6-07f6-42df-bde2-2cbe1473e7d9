import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { DialogHeader, DialogFooter } from '@/components/ui/dialog'
import type { UserProfile } from '@/services/site.api'
import React from 'react'
import { signOut } from '@/lib/auth-client'

interface AuthAvatarProps {
  user: UserProfile
  onLogout?: () => void
}

export function AuthAvatar({ ...props }: AuthAvatarProps) {
  const { user, onLogout } = props
  const [showLogoutDialog, setShowLogoutDialog] = React.useState(false)

  async function handleLogout(): Promise<void> {
    // remove session token from cookie
    try {
      // const { refresh_token, access_token } = await cmsClient.refresh()
      // window.location.reload()
      const resp = await signOut()
      console.log('logout resp', resp)
      onLogout?.()
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <>
      <div className="hidden md:flex items-center gap-2">
        {/* <span className="text-sm text-muted-foreground">Hi</span> */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Avatar className="cursor-pointer">
              {user.image && (
                <AvatarImage src={user.image} alt={user.name || 'User'} />
              )}
              <AvatarFallback className="border">
                {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
              </AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{user.name}</DropdownMenuLabel>
            <DropdownMenuLabel className="text-muted-foreground">
              {user.email}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                variant="destructive"
                onClick={() => setShowLogoutDialog(true)}
              >
                {/* <LogOut className="h-4 w-4" /> */}
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Dialog open={showLogoutDialog} onOpenChange={setShowLogoutDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to log out?</DialogTitle>
            <DialogDescription>
              You will be logged out of your account.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowLogoutDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                await handleLogout()
                setShowLogoutDialog(false)
              }}
            >
              Log out
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
