import type { QuoteSectionData } from '@/services/site.api'

export interface QuoteSectionProps {
  data: QuoteSectionData
}

export function QuoteSection({ ...props }: QuoteSectionProps) {
  const quoteSection = props.data

  return (
    <section
      id="quotes"
      className="flex flex-col items-center justify-center gap-8 w-full p-14 bg-accent z-20"
    >
      <blockquote className="max-w-3xl text-left px-4">
        <p className="text-lg md:text-xl text-muted-background leading-relaxed tracking-tighter font-medium mb-6">
          {quoteSection.quote}
        </p>

        <div className="flex gap-4">
          <div className="size-10 rounded-full bg-primary border border-border">
            <img
              src={quoteSection.author_image}
              alt={quoteSection.author_name}
              className="size-full rounded-full object-contain"
            />
          </div>
          <div className="text-left">
            <cite className="text-lg font-medium text-primary not-italic">
              {quoteSection.author_name}
            </cite>
            {quoteSection.author_role && (
              <p className="text-sm text-primary">{quoteSection.author_role}</p>
            )}
          </div>
        </div>
      </blockquote>
    </section>
  )
}
