import { Link } from '@tanstack/react-router'
import { GoStack } from 'react-icons/go'
import type { HeroData, UserProfile } from '@/services/site.api'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { HeroVideoDialog } from '@/components/ui/hero-video-dialog'
import { Input } from '@/components/ui/input'

interface HeroSectionProps {
  data: HeroData
  user: UserProfile | null
}

export function HeroSection({ ...props }: HeroSectionProps) {
  const { data: hero, user } = props

  return (
    <section id="hero" className="w-full relative">
      <div className="relative flex flex-col items-center w-full px-6">
        <div className="absolute inset-0">
          <div className="absolute inset-0 -z-10 h-[600px] md:h-[800px] w-full [background:radial-gradient(125%_125%_at_50%_10%,var(--background)_40%,var(--secondary)_100%)] rounded-b-xl"></div>
        </div>
        <div className="relative z-10 pt-32 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center">
          <p className="border border-border bg-accent rounded-full text-sm h-8 px-3 flex items-center gap-2">
            <GoStack className="h-4 w-4" />
            {hero.badge}
          </p>
          <div className="flex flex-col items-center justify-center gap-5">
            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center text-primary">
              {hero.title}
            </h1>
            <p className="text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight">
              {hero.description}
            </p>
          </div>
          {user?.id ? (
            <div className="w-full max-w-xl">
              <div className="relative flex items-center w-full bg-white dark:bg-background rounded-xl border border-input shadow-sm overflow-hidden">
                <div className="flex items-center pl-4 text-muted-foreground">
                  <Icons.logo className="h-8 w-8" />
                </div>
                <Input
                  id="query-content"
                  placeholder="Enter your idea here..."
                  className="border-0 shadow-none focus-visible:ring-0 focus-visible:border-0 h-14"
                />
                <Button
                  variant={'secondary'}
                  onClick={() => {
                    // Handle the query submission
                    console.log('Query submitted')
                  }}
                  className="absolute right-2 text-white h-10 rounded-lg px-6 font-medium"
                >
                  Generate →
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2.5 flex-wrap justify-center">
              <Link
                // onClick={() => {
                //   redirect({ to: '/signin' })
                //   // redirect({ to: hero.cta_primary_href || 'signin' })
                // }}
                className="bg-secondary h-9 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-32 px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95"
                to={'/signin'}
              >
                {hero.cta_primary_text}
              </Link>
              <Button
                onClick={() => {
                  window.open(hero.cta_secondary_href, '_blank')
                  // redirect({ to: hero.cta.secondary.href || '#' })
                }}
                className="h-10 flex items-center justify-center w-32 px-5 text-sm font-normal tracking-wide text-primary rounded-full transition-all ease-out active:scale-95 bg-white dark:bg-background border border-[#E5E7EB] dark:border-[#27272A] hover:bg-white/80 dark:hover:bg-background/80"
              >
                {hero.cta_secondary_text}
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className="relative px-6 mt-10">
        <div className="relative size-full shadow-xl rounded-2xl overflow-hidden">
          <HeroVideoDialog
            className="block dark:hidden"
            animationStyle="from-center"
            videoSrc={hero.video}
            thumbnailSrc={hero.video_thumbnail ?? '/hero-thumbnail.png'}
            thumbnailAlt="Hero Video"
          />
          <HeroVideoDialog
            className="hidden dark:block"
            animationStyle="from-center"
            videoSrc={hero.video}
            thumbnailSrc={hero.video_thumbnail ?? '/hero-thumbnail.png'}
            thumbnailAlt="Hero Video"
          />
        </div>
      </div>
    </section>
  )
}
