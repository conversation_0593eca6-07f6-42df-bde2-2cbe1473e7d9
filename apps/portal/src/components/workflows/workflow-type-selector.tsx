import { WorkflowNames } from '@repo/constants'
import { WORKFLOW_CONFIGS } from '@/types/workflow-form'
import { cn } from '@/lib/utils'

interface WorkflowTypeSelectorProps {
  value: WorkflowNames
  onChange: (value: WorkflowNames) => void
  error?: string
}

export function WorkflowTypeSelector({
  value,
  onChange,
  error,
}: WorkflowTypeSelectorProps) {
  return (
    <div className="space-y-3">
      <div className="grid gap-3">
        {Object.entries(WORKFLOW_CONFIGS).map(([type, config]) => (
          <button
            key={type}
            type="button"
            onClick={() => onChange(type as WorkflowNames)}
            className={cn(
              'group relative flex items-start gap-4 rounded-lg border p-4 text-left transition-all',
              'hover:border-gray-300 hover:bg-gray-50 dark:hover:border-gray-700 dark:hover:bg-gray-900',
              value === type
                ? 'border-blue-500 bg-blue-50 dark:border-blue-500 dark:bg-blue-950/20'
                : 'border-gray-200 dark:border-gray-800',
              error && 'border-red-300',
            )}
          >
            <div className="text-2xl">{config.icon}</div>
            <div className="flex-1 space-y-1">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                {config.label}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {config.description}
              </p>
            </div>
            <div
              className={cn(
                'h-5 w-5 rounded-full border-2 transition-all',
                value === type
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300 dark:border-gray-700',
              )}
            >
              {value === type && (
                <svg
                  className="h-full w-full text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
          </button>
        ))}
      </div>
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  )
}
