import { Button } from '@/components/ui/button'

interface WorkflowErrorProps {
  error: Error | unknown
  onRetry?: () => void
}

export function WorkflowError({ error, onRetry }: WorkflowErrorProps) {
  const errorMessage =
    error instanceof Error ? error.message : 'An unexpected error occurred'

  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-red-200 bg-red-50/50 p-12 text-center dark:border-red-900 dark:bg-red-950/20">
      <div className="mb-4 rounded-full bg-red-100 p-3 dark:bg-red-900/50">
        <svg
          className="h-8 w-8 text-red-600 dark:text-red-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
      </div>

      <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
        Failed to load workflows
      </h3>

      <p className="mb-6 max-w-md text-sm text-gray-600 dark:text-gray-400">
        {errorMessage}
      </p>

      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          Try again
        </Button>
      )}
    </div>
  )
}
