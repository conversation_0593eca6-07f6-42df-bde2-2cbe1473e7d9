import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { WorkflowTypeSelector } from './workflow-type-selector'
import { useCreateWorkflow } from '@/hooks/use-create-workflow'
import { WORKFLOW_CONFIGS } from '@/types/workflow-form'
import { AlertCircle, Loader2, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CreateWorkflowDialogProps {
  children?: React.ReactNode
  onSuccess?: (runId: string) => void
}

export function CreateWorkflowDialog({
  children,
  onSuccess,
}: CreateWorkflowDialogProps) {
  const [open, setOpen] = useState(false)
  const {
    formData,
    errors,
    isSubmitting,
    handleSubmit,
    updateField,
    resetForm,
  } = useCreateWorkflow({
    onSuccess: (runId) => {
      setOpen(false)
      onSuccess?.(runId)
    },
  })

  const currentConfig = WORKFLOW_CONFIGS[formData.workflowType]
  const characterCount = formData.campaignDescription.length

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (!newOpen) {
      // Reset form when dialog closes
      setTimeout(resetForm, 200) // Delay to allow animation
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children || (
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            Create Workflow
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Workflow</DialogTitle>
            <DialogDescription>
              Choose a workflow type and provide details to get started.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-6">
            {/* Workflow Type Selection */}
            <div className="space-y-2">
              <Label>Workflow Type</Label>
              <WorkflowTypeSelector
                value={formData.workflowType}
                onChange={(value) => updateField('workflowType', value)}
                error={errors.workflowType}
              />
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <Label htmlFor="campaignDescription">
                {currentConfig.fieldLabel}
              </Label>
              <div className="relative">
                <Textarea
                  id="campaignDescription"
                  name="campaignDescription"
                  placeholder={currentConfig.placeholder}
                  className={cn(
                    'min-h-[120px] resize-none pr-16',
                    errors.campaignDescription &&
                      'border-red-500 focus:ring-red-500',
                  )}
                  value={formData.campaignDescription}
                  onChange={(e) =>
                    updateField('campaignDescription', e.target.value)
                  }
                  disabled={isSubmitting}
                  maxLength={1000}
                />
                <div className="absolute bottom-2 right-2 text-xs text-gray-500">
                  {characterCount}/1000
                </div>
              </div>
              {errors.campaignDescription && (
                <div className="flex items-center gap-1 text-sm text-red-500">
                  <AlertCircle className="h-3 w-3" />
                  {errors.campaignDescription}
                </div>
              )}
            </div>

            {/* General Error */}
            {errors.submit && (
              <div className="rounded-md bg-red-50 p-3 text-sm text-red-800 dark:bg-red-950/50 dark:text-red-400">
                {errors.submit}
              </div>
            )}
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="gap-2">
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Workflow'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
