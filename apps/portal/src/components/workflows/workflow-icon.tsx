import { DefaultWorkflowIcon } from './workflow-icons'

interface WorkflowIconProps {
  name: string
  size?: number
  className?: string
}

export function WorkflowIcon({
  name,
  size = 64,
  className = '',
}: WorkflowIconProps) {
  // For emoji icons
  if (name.length <= 2) {
    return (
      <div
        className={`flex items-center justify-center ${className}`}
        style={{ width: size, height: size, fontSize: size * 0.75 }}
      >
        {name}
      </div>
    )
  }

  // For custom icon components (future expansion)
  switch (name) {
    case 'default':
      return <DefaultWorkflowIcon size={size} />
    default:
      return <DefaultWorkflowIcon size={size} />
  }
}
