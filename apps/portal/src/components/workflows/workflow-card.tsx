import { Link } from '@tanstack/react-router'
import { StatusBadge } from '@/components/status-badge'
import { WorkflowIcon } from './workflow-icon'
import { MoreOptionsIcon } from './workflow-icons'
import { WorkflowMetadata } from './workflow-metadata'
import type { WorkflowUI } from '@/types/workflow'

interface WorkflowCardProps {
  workflow: WorkflowUI
}

export function WorkflowCard({ workflow }: WorkflowCardProps) {
  return (
    <Link
      className="group relative block h-full overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-all duration-200 hover:border-gray-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-900 dark:hover:border-gray-700"
      to="/workflows/$wid"
      params={{ wid: workflow.id }}
      aria-label={`View ${workflow.title} workflow`}
    >
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="mb-auto flex items-start justify-between">
          <div className="transition-transform duration-200 group-hover:scale-105">
            <WorkflowIcon name={workflow.icon} size={64} />
          </div>

          <button
            className="rounded-lg p-2 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
            onClick={(e) => {
              e.preventDefault()
              // TODO: Implement workflow options menu
              console.log('Options clicked for:', workflow.id)
            }}
            aria-label="More options"
          >
            <MoreOptionsIcon />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6 space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {workflow.title}
          </h3>

          <div className="flex items-center justify-between">
            <StatusBadge status={workflow.status} />
            <WorkflowMetadata createdAt={workflow.createdAt} />
          </div>

          <div className="pt-2 text-sm text-gray-500 dark:text-gray-400">
            View details →
          </div>
        </div>
      </div>
    </Link>
  )
}
