import { Clock, Calendar, Activity } from 'lucide-react'
import { formatCompactDate, formatSmartDate } from '@/utils/date-utils'
import { cn } from '@/lib/utils'

interface WorkflowMetadataProps {
  createdAt: Date
  updatedAt?: Date
  className?: string
  variant?: 'compact' | 'detailed'
}

export function WorkflowMetadata({
  createdAt,
  updatedAt,
  className,
  variant = 'compact',
}: WorkflowMetadataProps) {
  const showUpdated = updatedAt && updatedAt.getTime() !== createdAt.getTime()

  if (variant === 'compact') {
    return (
      <div
        className={cn(
          'flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400',
          className,
        )}
      >
        <Clock className="h-3 w-3" />
        <span title={formatSmartDate(createdAt)}>
          {formatCompactDate(createdAt)}
        </span>
      </div>
    )
  }

  return (
    <div
      className={cn(
        'space-y-2 text-sm text-gray-600 dark:text-gray-400',
        className,
      )}
    >
      <div className="flex items-center gap-2">
        <Calendar className="h-4 w-4" />
        <span>Created {formatSmartDate(createdAt)}</span>
      </div>
      {showUpdated && (
        <div className="flex items-center gap-2">
          <Activity className="h-4 w-4" />
          <span>Updated {formatSmartDate(updatedAt)}</span>
        </div>
      )}
    </div>
  )
}
