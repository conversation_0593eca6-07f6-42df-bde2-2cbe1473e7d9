import { CreateWorkflowDialog } from './create-workflow-dialog'
import { EmptyStateIcon } from './workflow-icons'

export function WorkflowsEmptyState() {
  return (
    <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border-2 border-dashed border-gray-200 bg-gray-50/50 p-12 text-center dark:border-gray-800 dark:bg-gray-900/50">
      <EmptyStateIcon />

      <h3 className="mt-6 text-xl font-semibold text-gray-900 dark:text-gray-100">
        No workflows yet
      </h3>

      <p className="mt-2 max-w-sm text-sm text-gray-600 dark:text-gray-400">
        Get started by creating your first workflow. Choose from our templates
        or build your own custom automation.
      </p>

      <div className="mt-6">
        <CreateWorkflowDialog />
      </div>
    </div>
  )
}
