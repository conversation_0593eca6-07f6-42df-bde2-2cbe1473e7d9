export function WorkflowSkeleton() {
  return (
    <div className="animate-pulse rounded-xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
      <div className="flex h-full flex-col">
        {/* Header skeleton */}
        <div className="mb-auto flex items-start justify-between">
          <div className="h-16 w-16 rounded-full bg-gray-200 dark:bg-gray-800" />
          <div className="h-10 w-10 rounded-lg bg-gray-200 dark:bg-gray-800" />
        </div>

        {/* Content skeleton */}
        <div className="mt-6 space-y-3">
          <div className="h-6 w-3/4 rounded bg-gray-200 dark:bg-gray-800" />
          <div className="flex items-center justify-between">
            <div className="h-6 w-20 rounded-full bg-gray-200 dark:bg-gray-800" />
            <div className="h-4 w-16 rounded bg-gray-200 dark:bg-gray-800" />
          </div>
        </div>
      </div>
    </div>
  )
}

export function WorkflowGridSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: count }).map((_, i) => (
        <WorkflowSkeleton key={i} />
      ))}
    </div>
  )
}
