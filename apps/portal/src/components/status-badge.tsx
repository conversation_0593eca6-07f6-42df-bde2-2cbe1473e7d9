import * as React from 'react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

export type StatusType =
  | 'success'
  | 'running'
  | 'failed'
  | 'pending'
  | 'warning'
  | string

interface StatusBadgeProps extends React.ComponentProps<typeof Badge> {
  status: StatusType
  showDot?: boolean
  size?: 'sm' | 'default' | 'lg'
}

const statusStyles: Record<
  StatusType,
  { bg: string; text: string; dot: string }
> = {
  success: {
    bg: 'bg-emerald-50',
    text: 'text-emerald-700',
    dot: 'bg-emerald-500',
  },
  running: {
    bg: 'bg-blue-50',
    text: 'text-blue-700',
    dot: 'bg-blue-500',
  },
  failed: {
    bg: 'bg-red-50',
    text: 'text-red-700',
    dot: 'bg-red-500',
  },
  pending: {
    bg: 'bg-yellow-50',
    text: 'text-yellow-700',
    dot: 'bg-yellow-500',
  },
  warning: {
    bg: 'bg-orange-50',
    text: 'text-orange-700',
    dot: 'bg-orange-500',
  },
}

export function StatusBadge({
  status,
  showDot = true,
  size = 'default',
  className,
  children,
  ...props
}: StatusBadgeProps) {
  const styles = statusStyles[status] || statusStyles.pending

  const sizeStyles = {
    sm: 'text-xs py-0.5 px-2',
    default: 'text-xs py-1 px-2.5',
    lg: 'text-sm py-1 px-3',
  }

  const dotSizes = {
    sm: 'size-1',
    default: 'size-1.5',
    lg: 'size-2',
  }

  return (
    <Badge
      variant="outline"
      className={cn(
        'gap-1.5 border-transparent',
        styles.bg,
        styles.text,
        sizeStyles[size],
        className,
      )}
      {...props}
    >
      {showDot && (
        <span
          className={cn('rounded-full', dotSizes[size], styles.dot)}
          aria-hidden="true"
        />
      )}
      <span className="text-primary font-semibold">
        {children || status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    </Badge>
  )
}
