import * as v from 'valibot'

// Define the schema for client environment variables
const clientSchema = v.object({
  VITE_ORIGIN_URL: v.pipe(v.string(), v.minLength(1), v.url()),
})

// Create a safe version of the environment
let clientEnv: v.InferInput<typeof clientSchema>

try {
  // Use import.meta.env for client-side environment variables
  clientEnv = v.parse(clientSchema, import.meta.env)
} catch (error) {
  console.error('❌ Invalid client environment variables:', error)
  throw error
}

export const env = clientEnv
