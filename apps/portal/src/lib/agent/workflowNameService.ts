import { mastraClient } from '@/lib/agent'
import { workflowDbService } from '@/db/services'
import { extractFirstJson } from '@/lib/utils'

// Schema for the workflow name generation response
interface WorkflowNameResponse {
  workflowName: string
  reasoning?: string
}

/**
 * Portal-specific service for generating workflow names using the agent
 */
export class PortalWorkflowNameService {
  /**
   * Generate a descriptive name for a workflow using the agent
   * @param workflowType The type/name of the workflow
   * @param inputParameters The parameters passed to the workflow
   * @param timestamp Optional timestamp when the workflow was created
   * @returns Generated workflow name and reasoning
   */
  async generateWorkflowName(
    workflowType: string,
    inputParameters: any,
    timestamp?: Date,
  ): Promise<WorkflowNameResponse> {
    try {
      // Prepare the input for the agent
      const agentInput = {
        workflowType,
        inputParameters,
        timestamp: timestamp?.toISOString() || new Date().toISOString(),
      }

      // Use the Mastra client to get the agent and call it
      const agent = mastraClient.getAgent('workflowNameGenerator')
      const response = await agent.generate({
        messages: [
          {
            role: 'user',
            content: JSON.stringify(agentInput, null, 2),
          },
        ],
      })

      // Extract and parse the response
      const assistantMessage = response.response.messages[0]
      const content = Array.isArray(assistantMessage.content)
        ? assistantMessage.content[0]
        : assistantMessage.content

      const responseText =
        typeof content === 'string' ? content : (content as any).text || ''

      // Try to extract JSON from the response
      let parsedResult: any
      try {
        parsedResult = extractFirstJson(responseText)
      } catch (parseError) {
        console.warn('Failed to parse agent response as JSON:', parseError)
        throw new Error('Invalid response format from agent')
      }

      // Validate the response structure
      if (
        !parsedResult.workflowName ||
        typeof parsedResult.workflowName !== 'string'
      ) {
        throw new Error('Invalid response structure from agent')
      }

      return {
        workflowName: parsedResult.workflowName,
        reasoning: parsedResult.reasoning,
      }
    } catch (error) {
      console.error('Error generating workflow name via agent:', error)

      // Fallback to a generic name based on workflow type and timestamp
      const fallbackName = this.generateFallbackName(
        workflowType,
        inputParameters,
      )
      return {
        workflowName: fallbackName,
        reasoning: 'Generated using fallback logic due to agent error',
      }
    }
  }

  /**
   * Generate a fallback name when agent generation fails
   * @param workflowType The type of workflow
   * @param inputParameters The workflow parameters
   * @returns A fallback workflow name
   */
  private generateFallbackName(
    workflowType: string,
    inputParameters: any,
  ): string {
    const timestamp = new Date().toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })

    // Extract key information from parameters for common workflow types
    if (workflowType === 'creatorScoutWorkflow') {
      const description = inputParameters?.targetCreatorDescription
      if (description) {
        // Extract key terms from description
        const keyTerms = this.extractKeyTerms(description)
        if (keyTerms.length > 0) {
          return `Scout ${keyTerms.slice(0, 2).join(' ')} Creators`
        }
      }
      return `Creator Scout - ${timestamp}`
    }

    // Generic fallback
    const workflowDisplayName = workflowType
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim()

    return `${workflowDisplayName} - ${timestamp}`
  }

  /**
   * Extract key terms from a description for naming
   * @param description The description text
   * @returns Array of key terms
   */
  private extractKeyTerms(description: string): string[] {
    // Simple keyword extraction - look for important terms
    const keywords = [
      'gaming',
      'fashion',
      'beauty',
      'food',
      'travel',
      'tech',
      'fitness',
      'music',
      'dance',
      'comedy',
      'education',
      'lifestyle',
      'business',
      'mobile',
      'console',
      'pc',
      'indie',
      'aaa',
      'casual',
      'influencer',
      'creator',
      'streamer',
      'youtuber',
      'tiktoker',
      'asia',
      'europe',
      'america',
      'global',
      'local',
      'regional',
    ]

    const lowerDescription = description.toLowerCase()
    const foundKeywords = keywords.filter((keyword) =>
      lowerDescription.includes(keyword),
    )

    // Also extract capitalized words (likely proper nouns or important terms)
    const capitalizedWords = description.match(/\b[A-Z][a-z]+\b/g) || []

    return [...foundKeywords, ...capitalizedWords.map((w) => w.toLowerCase())]
      .filter((term, index, arr) => arr.indexOf(term) === index) // Remove duplicates
      .slice(0, 3) // Limit to 3 terms
  }

  /**
   * Update a workflow's name asynchronously
   * @param workflowRunId The workflow run ID
   * @param workflowType The type of workflow
   * @param inputParameters The parameters used to create the workflow
   * @param timestamp Optional timestamp when the workflow was created
   */
  async updateWorkflowNameAsync(
    workflowRunId: string,
    workflowType: string,
    inputParameters: any,
    timestamp?: Date,
  ): Promise<void> {
    try {
      console.log(`🏷️ Generating name for workflow ${workflowRunId}...`)

      // Generate the workflow name
      const { workflowName, reasoning } = await this.generateWorkflowName(
        workflowType,
        inputParameters,
        timestamp,
      )

      // Update the workflow name in the database
      await workflowDbService.updateWorkflowName(workflowRunId, workflowName)

      console.log(
        `✅ Updated workflow ${workflowRunId} name to: "${workflowName}"`,
      )
      if (reasoning) {
        console.log(`💡 Reasoning: ${reasoning}`)
      }
    } catch (error) {
      console.error(
        `❌ Failed to update workflow name for ${workflowRunId}:`,
        error,
      )
      // Don't throw the error - this is a non-critical background operation
    }
  }

  /**
   * Schedule an asynchronous workflow name update (fire and forget)
   * @param workflowRunId The workflow run ID
   * @param workflowType The type of workflow
   * @param inputParameters The parameters used to create the workflow
   * @param timestamp Optional timestamp when the workflow was created
   */
  scheduleWorkflowNameUpdate(
    workflowRunId: string,
    workflowType: string,
    inputParameters: any,
    timestamp?: Date,
  ): void {
    // Use setImmediate to schedule the update for the next tick of the event loop
    setImmediate(() => {
      this.updateWorkflowNameAsync(
        workflowRunId,
        workflowType,
        inputParameters,
        timestamp,
      ).catch((error) => {
        console.error(
          `Background workflow name update failed for ${workflowRunId}:`,
          error,
        )
      })
    })
  }
}

// Export a singleton instance
export const portalWorkflowNameService = new PortalWorkflowNameService()

/**
 * Utility function to schedule an asynchronous workflow name update from the portal
 * @param workflowRunId The workflow run ID
 * @param workflowType The type of workflow
 * @param inputParameters The parameters used to create the workflow
 * @param timestamp Optional timestamp when the workflow was created
 */
export function scheduleWorkflowNameUpdate(
  workflowRunId: string,
  workflowType: string,
  inputParameters: any,
  timestamp?: Date,
): void {
  portalWorkflowNameService.scheduleWorkflowNameUpdate(
    workflowRunId,
    workflowType,
    inputParameters,
    timestamp,
  )
}

/**
 * Utility function to generate a workflow name immediately
 * @param workflowType The type of workflow
 * @param inputParameters The parameters used to create the workflow
 * @param timestamp Optional timestamp when the workflow was created
 * @returns Promise that resolves to the generated workflow name and reasoning
 */
export async function generateWorkflowName(
  workflowType: string,
  inputParameters: any,
  timestamp?: Date,
): Promise<WorkflowNameResponse> {
  return portalWorkflowNameService.generateWorkflowName(
    workflowType,
    inputParameters,
    timestamp,
  )
}
