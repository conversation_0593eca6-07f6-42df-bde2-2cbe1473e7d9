'use server'

import { <PERSON><PERSON><PERSON><PERSON> } from '@mastra/client-js'
import { type WorkflowWatchEvent } from '@repo/constants'
import { env } from '@/env/server'
import { workflowDbService } from '@/db/services'
import type { WorkflowSnapshotState } from '@repo/constants'

// Initialize the client
export const mastraClient = new MastraClient({
  baseUrl: env.MASTRA_API_URL,
})

/**
 * Get all workflows for a user
 * @param userId The user ID
 * @returns Array of user workflow records
 */
export const getUserWorkflows = async (userId: string) => {
  return await workflowDbService.getUserWorkflows(userId)
}

/**
 * Get a specific workflow by run ID
 * @param workflowRunId The workflow run ID
 * @returns The workflow record or null if not found
 */
export const getWorkflowByRunId = async (workflowRunId: string) => {
  return await workflowDbService.getWorkflowByRunId(workflowRunId)
}

/**
 * Get workflow context data for a specific workflow run
 * @param workflowRunId The workflow run ID
 * @returns The workflow context data
 */
export const getWorkflowContexts = async (workflowRunId: string) => {
  return await workflowDbService.getAllWorkflowContexts(workflowRunId)
}

/**
 * Create a new workflow run and link it to a user
 * @param userId The user ID
 * @param workflowId The name of the workflow to run
 * @param inputData The data to trigger the workflow with
 * @returns The workflow run ID and start function
 */
export const createWorkflowAndRun = async (
  userId: string,
  workflowId: string,
  inputData: any,
  onRecord: (record: WorkflowWatchEvent) => void,
) => {
  // Get the record of workflow responses from Mastra
  const workflowsRecord = await mastraClient.getWorkflows()

  // console.log('Parameters:', userId, workflowName, triggerData)

  // console.log('workflowsRecord', workflowsRecord)

  // Convert the record's values into an array of workflow definitions.
  // const availableWorkflows = Object.values(workflowsRecord).map(
  //   (wfResponse) => ({
  //     name: wfResponse.name,
  //     id: wfResponse.workflowId,
  //   }),
  // )
  // const availableWorkflows = Object.entries(workflowsRecord).map(
  //   ([name, wfResponse]) => ({
  //     name,
  //     id: name,
  //   }),
  // )

  // console.log('Available workflows:', availableWorkflows)

  // Check if the array is empty
  if (Object.values(workflowsRecord).length === 0) {
    console.error(
      'No workflows found or failed to parse workflows from API response.',
    )
    throw new Error('Could not retrieve workflows.')
  }

  // Find the specific workflow definition from the array.
  // const workflowDefinition = availableWorkflows.find(
  //   (wf) => wf.name === workflowName,
  // )

  // if (!workflowDefinition || !workflowDefinition.id) {
  //   console.error(`Workflow "${workflowName}" not found or is missing an ID.`)
  //   throw new Error(`Workflow "${workflowName}" not found or invalid.`)
  // }

  // Get the executable workflow instance using its ID.
  const workflow = mastraClient.getWorkflow(workflowId)

  // Create a new run for this workflow, awaiting the promise.
  const { runId: newRunId } = await workflow.createRun()

  // Use the new runId for linking.
  const runId = newRunId

  // Link the workflow to the user
  await workflowDbService.linkWorkflowToUser(userId, runId, workflowId)

  // Watch the workflow run
  workflow.watch({ runId }, onRecord)

  // We use to await this workflow to finish, but now we just start it and watch its steps
  workflow.start({
    runId: newRunId,
    inputData,
  })

  // Start the workflow run asynchronously.
  // await workflow.startAsync({
  //   runId: newRunId,
  //   triggerData,
  // })

  return { runId }
}

/**
 * Get workflow snapshot for a specific workflow run
 * @param workflowRunId The workflow run ID
 * @returns The workflow snapshot record
 */
export const getWorkflowSnapshot = async (workflowRunId: string) => {
  const snapshotRecord =
    await workflowDbService.getWorkflowSnapshot(workflowRunId)
  if (!snapshotRecord) {
    throw new Error('Workflow snapshot not found for run ID: ' + workflowRunId)
  }

  return {
    ...snapshotRecord,
    snapshot: JSON.parse(snapshotRecord.snapshot) as WorkflowSnapshotState,
  }
}
