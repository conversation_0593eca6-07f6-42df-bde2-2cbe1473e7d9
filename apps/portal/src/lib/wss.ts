import { env } from '@/env/server'

const wss = new WebSocket(env.WS_SERVER_URL)

export const sendMessage = (message: string) => {
  wss.send(message)
}

export const onMessage = (callback: (message: string) => void) => {
  wss.onmessage = (event) => {
    callback(event.data)
  }
}

wss.onopen = () => {
  console.log('WebSocket connection established')
}

wss.onclose = () => {
  console.log('WebSocket connection closed')
}

wss.onerror = (error) => {
  console.error('WebSocket error:', error)
}

export default wss
