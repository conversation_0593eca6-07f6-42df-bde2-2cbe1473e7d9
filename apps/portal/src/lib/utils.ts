import { clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import * as Color from 'color-bits'
import type { ClassValue } from 'clsx'

export function cn(...inputs: Array<ClassValue>) {
  return twMerge(clsx(inputs))
}

// Helper function to convert any CSS color to rgba
export const getRGBA = (
  cssColor: React.CSSProperties['color'],
  fallback: string = 'rgba(180, 180, 180)',
): string => {
  if (typeof window === 'undefined') return fallback
  if (!cssColor) return fallback

  try {
    // Handle CSS variables
    if (typeof cssColor === 'string' && cssColor.startsWith('var(')) {
      const element = document.createElement('div')
      element.style.color = cssColor
      document.body.appendChild(element)
      const computedColor = window.getComputedStyle(element).color
      document.body.removeChild(element)
      return Color.formatRGBA(Color.parse(computedColor))
    }

    return Color.formatRGBA(Color.parse(cssColor))
  } catch (e) {
    console.error('Color parsing failed:', e)
    return fallback
  }
}

// Helper function to add opacity to an RGB color string
export const colorWithOpacity = (color: string, opacity: number): string => {
  if (!color.startsWith('rgb')) return color
  return Color.formatRGBA(Color.alpha(Color.parse(color), opacity))
}

/**
 * Extracts JSON objects from a text string
 * @param text - The input text containing JSON objects
 * @param options - Configuration options
 * @returns Array of parsed JSON objects
 */
interface ExtractJsonOptions {
  /** Return only the first JSON object found */
  firstOnly?: boolean
  /** Allow parsing of JSON arrays as well as objects */
  includeArrays?: boolean
  /** Skip invalid JSON and continue parsing */
  skipInvalid?: boolean
}

function extractJsonFromText<T = any>(
  text: string,
  options: ExtractJsonOptions = {},
): T[] {
  // Use the advanced extraction method instead of regex for better accuracy
  return extractJsonAdvanced<T>(text, options)
}

/**
 * Extracts the first JSON object from text
 * @param text - The input text
 * @returns The first JSON object found, or null if none found
 */
function extractFirstJson<T = any>(text: string): T | null {
  const results = extractJsonFromText<T>(text, { firstOnly: true })
  return results.length > 0 ? results[0] : null
}

/**
 * More sophisticated JSON extraction that handles nested braces better
 * @param text - The input text
 * @param options - Configuration options
 * @returns Array of parsed JSON objects
 */
function extractJsonAdvanced<T = any>(
  text: string,
  options: ExtractJsonOptions = {},
): T[] {
  const {
    firstOnly = false,
    includeArrays = true,
    skipInvalid = true,
  } = options
  const results: T[] = []

  // Find all potential starting positions
  const startChars = includeArrays ? ['{', '['] : ['{']
  const endChars = includeArrays ? ['}', ']'] : ['}']

  for (let i = 0; i < text.length; i++) {
    const char = text[i]

    if (startChars.includes(char)) {
      const startChar = char
      const endChar = startChar === '{' ? '}' : ']'

      // Track bracket depth
      let depth = 1
      let j = i + 1
      let inString = false
      let escapeNext = false

      while (j < text.length && depth > 0) {
        const currentChar = text[j]

        if (escapeNext) {
          escapeNext = false
        } else if (currentChar === '\\') {
          escapeNext = true
        } else if (currentChar === '"' && !escapeNext) {
          inString = !inString
        } else if (!inString) {
          if (currentChar === startChar) {
            depth++
          } else if (currentChar === endChar) {
            depth--
          }
        }

        j++
      }

      if (depth === 0) {
        const jsonStr = text.substring(i, j)

        try {
          const parsed = JSON.parse(jsonStr)

          if (
            includeArrays
              ? typeof parsed === 'object' && parsed !== null
              : typeof parsed === 'object' &&
                parsed !== null &&
                !Array.isArray(parsed)
          ) {
            results.push(parsed)

            if (firstOnly) {
              break
            }
          }
        } catch (error) {
          if (!skipInvalid) {
            throw new Error(
              `Invalid JSON found: ${jsonStr.substring(0, 100)}...`,
            )
          }
        }
      }
    }
  }

  return results
}

// Export the functions
export {
  extractJsonFromText,
  extractFirstJson,
  extractJsonAdvanced,
  type ExtractJsonOptions,
}
