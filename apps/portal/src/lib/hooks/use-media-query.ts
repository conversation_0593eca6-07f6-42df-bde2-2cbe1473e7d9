'use client'

import { useEffect, useState } from 'react'

export function useMediaQuery(query: string) {
  const [value, setValue] = useState(false)

  useEffect(() => {
    // Handle initial check and subsequent changes
    function checkQuery() {
      const result = window.matchMedia(query)
      setValue(result.matches)
    }

    // Check immediately
    checkQuery()

    // Add resize listener
    window.addEventListener('resize', checkQuery)

    // Add media query change listener
    const mediaQuery = window.matchMedia(query)
    mediaQuery.addEventListener('change', checkQuery)

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkQuery)
      mediaQuery.removeEventListener('change', checkQuery)
    }
  }, [query])

  return value
}
