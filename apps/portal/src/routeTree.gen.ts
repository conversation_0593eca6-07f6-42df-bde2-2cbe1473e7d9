/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthImport } from './routes/auth'
import { Route as DashboardImport } from './routes/_dashboard'
import { Route as IndexImport } from './routes/index'
import { Route as DashboardPortalImport } from './routes/_dashboard/portal'
import { Route as authSignupImport } from './routes/(auth)/signup'
import { Route as authSigninImport } from './routes/(auth)/signin'
import { Route as DashboardWorkflowsWidImport } from './routes/_dashboard/workflows.$wid'

// Create/Update Routes

const AuthRoute = AuthImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/_dashboard',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardPortalRoute = DashboardPortalImport.update({
  id: '/portal',
  path: '/portal',
  getParentRoute: () => DashboardRoute,
} as any)

const authSignupRoute = authSignupImport.update({
  id: '/(auth)/signup',
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const authSigninRoute = authSigninImport.update({
  id: '/(auth)/signin',
  path: '/signin',
  getParentRoute: () => rootRoute,
} as any)

const DashboardWorkflowsWidRoute = DashboardWorkflowsWidImport.update({
  id: '/workflows/$wid',
  path: '/workflows/$wid',
  getParentRoute: () => DashboardRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_dashboard': {
      id: '/_dashboard'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/signin': {
      id: '/(auth)/signin'
      path: '/signin'
      fullPath: '/signin'
      preLoaderRoute: typeof authSigninImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/signup': {
      id: '/(auth)/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof authSignupImport
      parentRoute: typeof rootRoute
    }
    '/_dashboard/portal': {
      id: '/_dashboard/portal'
      path: '/portal'
      fullPath: '/portal'
      preLoaderRoute: typeof DashboardPortalImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/workflows/$wid': {
      id: '/_dashboard/workflows/$wid'
      path: '/workflows/$wid'
      fullPath: '/workflows/$wid'
      preLoaderRoute: typeof DashboardWorkflowsWidImport
      parentRoute: typeof DashboardImport
    }
  }
}

// Create and export the route tree

interface DashboardRouteChildren {
  DashboardPortalRoute: typeof DashboardPortalRoute
  DashboardWorkflowsWidRoute: typeof DashboardWorkflowsWidRoute
}

const DashboardRouteChildren: DashboardRouteChildren = {
  DashboardPortalRoute: DashboardPortalRoute,
  DashboardWorkflowsWidRoute: DashboardWorkflowsWidRoute,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof DashboardRouteWithChildren
  '/auth': typeof AuthRoute
  '/signin': typeof authSigninRoute
  '/signup': typeof authSignupRoute
  '/portal': typeof DashboardPortalRoute
  '/workflows/$wid': typeof DashboardWorkflowsWidRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof DashboardRouteWithChildren
  '/auth': typeof AuthRoute
  '/signin': typeof authSigninRoute
  '/signup': typeof authSignupRoute
  '/portal': typeof DashboardPortalRoute
  '/workflows/$wid': typeof DashboardWorkflowsWidRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_dashboard': typeof DashboardRouteWithChildren
  '/auth': typeof AuthRoute
  '/(auth)/signin': typeof authSigninRoute
  '/(auth)/signup': typeof authSignupRoute
  '/_dashboard/portal': typeof DashboardPortalRoute
  '/_dashboard/workflows/$wid': typeof DashboardWorkflowsWidRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/auth'
    | '/signin'
    | '/signup'
    | '/portal'
    | '/workflows/$wid'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '' | '/auth' | '/signin' | '/signup' | '/portal' | '/workflows/$wid'
  id:
    | '__root__'
    | '/'
    | '/_dashboard'
    | '/auth'
    | '/(auth)/signin'
    | '/(auth)/signup'
    | '/_dashboard/portal'
    | '/_dashboard/workflows/$wid'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRouteWithChildren
  AuthRoute: typeof AuthRoute
  authSigninRoute: typeof authSigninRoute
  authSignupRoute: typeof authSignupRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRouteWithChildren,
  AuthRoute: AuthRoute,
  authSigninRoute: authSigninRoute,
  authSignupRoute: authSignupRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_dashboard",
        "/auth",
        "/(auth)/signin",
        "/(auth)/signup"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_dashboard": {
      "filePath": "_dashboard.tsx",
      "children": [
        "/_dashboard/portal",
        "/_dashboard/workflows/$wid"
      ]
    },
    "/auth": {
      "filePath": "auth.tsx"
    },
    "/(auth)/signin": {
      "filePath": "(auth)/signin.tsx"
    },
    "/(auth)/signup": {
      "filePath": "(auth)/signup.tsx"
    },
    "/_dashboard/portal": {
      "filePath": "_dashboard/portal.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/workflows/$wid": {
      "filePath": "_dashboard/workflows.$wid.tsx",
      "parent": "/_dashboard"
    }
  }
}
ROUTE_MANIFEST_END */
