import { restrictEnvAccess } from '@repo/eslint-config/base'
import reactConfig from '@repo/eslint-config/react'
// import tanstackReactConfig from '@repo/eslint-config/tanstack'

/** @type {import("eslint").Linter.Config} */
export default [
  ...reactConfig,
  ...restrictEnvAccess,
  // ...tanstackReactConfig,
  {
    files: ['vite.config.ts'],
    rules: {
      'no-restricted-properties': 'off',
    },
  },
]
