{"include": ["src/**/*.ts", "src/**/*.tsx", "src/style.css", "env.ts", "vite-env.d.ts", "vite.config.js"], "compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "jsx": "react-jsx", "moduleResolution": "bundler", "noEmit": true, "strictNullChecks": true, "allowImportingTsExtensions": true, "declaration": true, "declarationMap": true, "inlineSources": false, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "preserveWatchOutput": true, "paths": {"@/*": ["./src/*"]}}, "exclude": ["node_modules"]}