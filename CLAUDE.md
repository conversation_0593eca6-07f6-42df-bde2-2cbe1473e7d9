# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a **Turborepo monorepo** with two main applications:

- **`apps/agent`**: AI agent system built on the **Mastra framework** for orchestrating multi-step AI workflows
- **`apps/portal`**: Web application built with **TanStack Start** (React) for workflow management UI
- **`packages/`**: Shared libraries (auth, db, constants) using **Drizzle ORM** and **Better Auth**
- **`tools/`**: Development tooling configurations

## Essential Commands

### Development
```bash
pnpm dev          # Start all apps in development mode
pnpm build        # Build all packages and apps
pnpm format:fix   # Auto-fix code formatting
pnpm lint:fix     # Auto-fix linting issues
pnpm typecheck    # Run TypeScript checks across workspace
```

### Database Operations
```bash
pnpm db:push      # Push schema changes to database
pnpm db:studio    # Open Drizzle Studio for database management
```

### App-Specific Commands
```bash
# Agent app (Mastra-based)
cd apps/agent && pnpm dev        # Start Mastra development server
cd apps/agent && pnpm db:migrate # Run agent database migrations

# Portal app (TanStack Start)
cd apps/portal && pnpm dev       # Start Vinxi development server
cd apps/portal && pnpm test      # Run Vitest tests
```

## Architecture Overview

### Agent System (Mastra Framework)
The agent system orchestrates AI workflows through:

- **5 Specialized Agents**: Campaign analyzer, creator scout, filter, hashtag scout, workflow name generator
- **Core Workflows**: `creatorScoutWorkflow` (4-step human-in-the-loop) and `aiScriptWorkflow`
- **Services**: Video scouting (TikHub/TokAPI integration), S3 storage, database operations
- **Key Pattern**: Multi-step workflows with parallel processing, retry logic, and database context storage

**Main Configuration**: `apps/agent/src/mastra/index.ts`

### Portal Web App (TanStack Ecosystem)
Modern React application with:

- **TanStack Router**: File-based routing with type safety
- **TanStack Query**: Server state management
- **Authentication**: Better Auth with protected dashboard routes
- **UI**: Radix UI components with Tailwind CSS styling
- **Key Routes**: Landing (`/`), auth (`/signin`, `/signup`), dashboard (`/_dashboard/*`)

**Main Router**: `apps/portal/src/router.tsx`

### Shared Packages
- **Database**: Drizzle ORM with PostgreSQL, shared schemas and operations
- **Auth**: Better Auth configuration for both client and server
- **Constants**: Workflow enums, validation schemas, shared types

## Key Development Patterns

### AI Integration
- Multiple providers supported (Google AI, OpenRouter) via `apps/agent/src/ai/providers.ts`
- Structured outputs using Zod schemas
- Temperature control for different task types
- Comprehensive retry logic with exponential backoff

### Workflow System
- Human-in-the-loop capability for interactive workflows
- Parallel processing with controlled concurrency limits
- Database context storage to handle large datasets efficiently
- Campaign mode with intelligent deduplication

### Data Flow
1. Portal UI creates workflows via TanStack Query mutations
2. Agent system processes through Mastra workflow orchestration
3. Results stored in database with context IDs for retrieval
4. Portal displays results with real-time status updates

## Testing and Code Quality

- **Linting**: ESLint with shared configurations
- **Formatting**: Prettier with workspace-wide settings
- **Type Checking**: Strict TypeScript across all packages
- **Testing**: Vitest for portal app, manual testing scripts for agent workflows

Always run `pnpm lint:fix && pnpm typecheck` before committing changes.